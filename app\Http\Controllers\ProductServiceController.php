<?php

namespace App\Http\Controllers;

use App\Models\ChartOfAccount;
use App\Models\ChartOfAccountType;
use App\Models\CustomField;
use App\Exports\ProductServiceExport;
use App\Imports\ProductServiceImport;
use App\Models\Product;
use App\Models\ProductService;
use App\Models\ProductServiceCategory;
use App\Models\ProductServiceUnit;
use App\Models\Tax;
use App\Models\User;
use App\Models\Utility;
use App\Models\Vender;
use App\Models\WarehouseProduct;
use Google\Service\Dataproc\Session;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;




class ProductServiceController extends Controller
{
    public function index(Request $request)
    {

        if (\Auth::user()->can('manage product & service')) {
            $category = ProductServiceCategory::where('created_by', '=', \Auth::user()->creatorId())->where('type', '=', 'product & service')->get()->pluck('name', 'id');
            $category->prepend('Select Category', '');

            if (!empty($request->category)) {

                $productServices = ProductService::where('created_by', '=', \Auth::user()->creatorId())->where('category_id', $request->category)->with(['category', 'unit'])->get();
            } else {
                $productServices = ProductService::where('created_by', '=', \Auth::user()->creatorId())->with(['category', 'unit'])->get();
            }

            return view('productservice.index', compact('productServices', 'category'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }


    public function create()
    {
        if (\Auth::user()->can('create product & service')) {
            $customFields = CustomField::where('created_by', '=', \Auth::user()->creatorId())->where('module', '=', 'product')->get();
            $category     = ProductServiceCategory::where('created_by', '=', \Auth::user()->creatorId())->where('type', '=', 'product & service')->get()->pluck('name', 'id');
            $unit         = ProductServiceUnit::where('created_by', '=', \Auth::user()->creatorId())->get()->pluck('name', 'id');
            $tax          = Tax::where('created_by', '=', \Auth::user()->creatorId())->get()->pluck('name', 'id');
            $incomeChartAccounts = ChartOfAccount::select(\DB::raw('CONCAT(chart_of_accounts.code, " - ", chart_of_accounts.name) AS code_name, chart_of_accounts.id as id'))
                ->leftjoin('chart_of_account_types', 'chart_of_account_types.id', 'chart_of_accounts.type')
                ->where('chart_of_account_types.name', 'income')
                ->where('parent', '=', 0)
                ->where('chart_of_accounts.created_by', \Auth::user()->creatorId())->get()
                ->pluck('code_name', 'id');
            $incomeChartAccounts->prepend('Select Account', 0);

            $incomeSubAccounts = ChartOfAccount::select(\DB::raw('CONCAT(chart_of_accounts.code, " - ", chart_of_accounts.name) AS code_name,chart_of_accounts.id, chart_of_accounts.code, chart_of_account_parents.account'));
            $incomeSubAccounts->leftjoin('chart_of_account_parents', 'chart_of_accounts.parent', 'chart_of_account_parents.id');
            $incomeSubAccounts->leftjoin('chart_of_account_types', 'chart_of_account_types.id', 'chart_of_accounts.type');
            $incomeSubAccounts->where('chart_of_account_types.name', 'income');
            $incomeSubAccounts->where('chart_of_accounts.parent', '!=', 0);
            $incomeSubAccounts->where('chart_of_accounts.created_by', \Auth::user()->creatorId());
            $incomeSubAccounts = $incomeSubAccounts->get()->toArray();


            $expenseChartAccounts = ChartOfAccount::select(\DB::raw('CONCAT(chart_of_accounts.code, " - ", chart_of_accounts.name) AS code_name, chart_of_accounts.id as id'))
                ->leftjoin('chart_of_account_types', 'chart_of_account_types.id', 'chart_of_accounts.type')
                ->whereIn('chart_of_account_types.name', ['Expenses', 'Costs of Goods Sold'])
                ->where('chart_of_accounts.created_by', \Auth::user()->creatorId())->get()
                ->pluck('code_name', 'id');
            $expenseChartAccounts->prepend('Select Account', '');

            $expenseSubAccounts = ChartOfAccount::select(\DB::raw('CONCAT(chart_of_accounts.code, " - ", chart_of_accounts.name) AS code_name,chart_of_accounts.id, chart_of_accounts.code, chart_of_account_parents.account'));
            $expenseSubAccounts->leftjoin('chart_of_account_parents', 'chart_of_accounts.parent', 'chart_of_account_parents.id');
            $expenseSubAccounts->leftjoin('chart_of_account_types', 'chart_of_account_types.id', 'chart_of_accounts.type');
            $expenseSubAccounts->whereIn('chart_of_account_types.name', ['Expenses', 'Costs of Goods Sold']);
            $expenseSubAccounts->where('chart_of_accounts.parent', '!=', 0);
            $expenseSubAccounts->where('chart_of_accounts.created_by', \Auth::user()->creatorId());
            $expenseSubAccounts = $expenseSubAccounts->get()->toArray();


            return view('productservice.create', compact('category', 'unit', 'tax', 'customFields', 'incomeChartAccounts', 'incomeSubAccounts', 'expenseChartAccounts', 'expenseSubAccounts'));
        } else {
            return response()->json(['error' => __('Permission denied.')], 401);
        }
    }

    public function store(Request $request)
    {
        if (\Auth::user()->can('create product & service')) {
            try {
                // Enhanced validation rules
                $rules = [
                    'name' => 'required|string|max:255',
                    'sku' => [
                        'required',
                        'string',
                        'max:255',
                        Rule::unique('product_services')->where(function ($query) {
                            return $query->where('created_by', \Auth::user()->creatorId());
                        })
                    ],
                    'sale_price' => 'required|numeric|min:0',
                    'purchase_price' => 'required|numeric|min:0',
                    'category_id' => 'required|exists:product_service_categories,id',
                    'unit_id' => 'required|exists:product_service_units,id',
                    'type' => 'required|in:product,service',
                    'sale_chartaccount_id' => 'required|exists:chart_of_accounts,id',
                    'expense_chartaccount_id' => 'required|exists:chart_of_accounts,id',
                    'quantity' => 'nullable|numeric|min:0',
                    'tax_id' => 'nullable|array',
                    'tax_id.*' => 'exists:taxes,id',
                    'description' => 'nullable|string|max:1000',
                    'pro_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
                ];

                $validator = \Validator::make($request->all(), $rules);

                if ($validator->fails()) {
                    $messages = $validator->getMessageBag();
                    \Log::error('Product validation failed: ' . $messages->first(), [
                        'user_id' => \Auth::user()->id,
                        'request_data' => $request->except(['pro_image', '_token'])
                    ]);
                    return redirect()->route('productservice.index')->with('error', $messages->first());
                }

                // Verify that the selected category and unit belong to the current user
                $category = ProductServiceCategory::where('id', $request->category_id)
                    ->where('created_by', \Auth::user()->creatorId())
                    ->first();

                if (!$category) {
                    return redirect()->route('productservice.index')->with('error', __('Selected category is invalid.'));
                }

                $unit = ProductServiceUnit::where('id', $request->unit_id)
                    ->where('created_by', \Auth::user()->creatorId())
                    ->first();

                if (!$unit) {
                    return redirect()->route('productservice.index')->with('error', __('Selected unit is invalid.'));
                }

                // Create new product service
                $productService = new ProductService();
                $productService->name = $request->name;
                $productService->description = $request->description;
                $productService->sku = $request->sku;
                $productService->sale_price = $request->sale_price;
                $productService->purchase_price = $request->purchase_price;
                $productService->tax_id = !empty($request->tax_id) ? implode(',', $request->tax_id) : '';
                $productService->unit_id = $request->unit_id;

                // Handle quantity based on type
                if ($request->type == 'product') {
                    $productService->quantity = $request->quantity ?? 0;
                } else {
                    $productService->quantity = 0; // Services don't have quantity
                }

                $productService->type = $request->type;
                $productService->sale_chartaccount_id = $request->sale_chartaccount_id;
                $productService->expense_chartaccount_id = $request->expense_chartaccount_id;
                $productService->category_id = $request->category_id;
                $productService->created_by = \Auth::user()->creatorId();

                // Handle image upload
                if (!empty($request->pro_image)) {
                    try {
                        $image_size = $request->file('pro_image')->getSize();
                        $result = Utility::updateStorageLimit(\Auth::user()->creatorId(), $image_size);

                        if ($result == 1) {
                            $fileName = time() . '_' . $request->pro_image->getClientOriginalName();
                            $productService->pro_image = $fileName;
                            $dir = 'uploads/pro_image';
                            $path = Utility::upload_file($request, 'pro_image', $fileName, $dir, []);
                        } else {
                            \Log::warning('Storage limit exceeded for user: ' . \Auth::user()->id);
                        }
                    } catch (\Exception $e) {
                        \Log::error('Image upload failed: ' . $e->getMessage(), [
                            'user_id' => \Auth::user()->id,
                            'product_name' => $request->name
                        ]);
                        // Continue without image if upload fails
                    }
                }

                // Save the product
                $productService->save();

                // Save custom fields if any
                if (!empty($request->customField)) {
                    CustomField::saveData($productService, $request->customField);
                }

                \Log::info('Product created successfully', [
                    'user_id' => \Auth::user()->id,
                    'product_id' => $productService->id,
                    'product_name' => $productService->name,
                    'sku' => $productService->sku
                ]);

                return redirect()->route('productservice.index')->with('success', __('Product successfully created.'));

            } catch (\Exception $e) {
                \Log::error('Product creation failed: ' . $e->getMessage(), [
                    'user_id' => \Auth::user()->id,
                    'request_data' => $request->except(['pro_image', '_token']),
                    'stack_trace' => $e->getTraceAsString()
                ]);

                return redirect()->route('productservice.index')->with('error', __('Failed to create product. Please try again.'));
            }
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function show()
    {
        return redirect()->route('productservice.index');
    }

    public function edit($id)
    {
        $productService = ProductService::find($id);

        if (\Auth::user()->can('edit product & service')) {
            if ($productService->created_by == \Auth::user()->creatorId()) {
                $category = ProductServiceCategory::where('created_by', '=', \Auth::user()->creatorId())->where('type', '=', 'product & service')->get()->pluck('name', 'id');
                $unit     = ProductServiceUnit::where('created_by', '=', \Auth::user()->creatorId())->get()->pluck('name', 'id');
                $tax      = Tax::where('created_by', '=', \Auth::user()->creatorId())->get()->pluck('name', 'id');

                $productService->customField = CustomField::getData($productService, 'product');
                $customFields                = CustomField::where('created_by', '=', \Auth::user()->creatorId())->where('module', '=', 'product')->get();
                $productService->tax_id      = explode(',', $productService->tax_id);
                $incomeChartAccounts = ChartOfAccount::select(\DB::raw('CONCAT(chart_of_accounts.code, " - ", chart_of_accounts.name) AS code_name, chart_of_accounts.id as id'))
                    ->leftjoin('chart_of_account_types', 'chart_of_account_types.id', 'chart_of_accounts.type')
                    ->where('chart_of_account_types.name', 'income')
                    ->where('parent', '=', 0)
                    ->where('chart_of_accounts.created_by', \Auth::user()->creatorId())->get()
                    ->pluck('code_name', 'id');
                $incomeChartAccounts->prepend('Select Account', 0);


                $incomeSubAccounts = ChartOfAccount::select(\DB::raw('CONCAT(chart_of_accounts.code, " - ", chart_of_accounts.name) AS code_name,chart_of_accounts.id, chart_of_accounts.code, chart_of_account_parents.account'));
                $incomeSubAccounts->leftjoin('chart_of_account_parents', 'chart_of_accounts.parent', 'chart_of_account_parents.id');
                $incomeSubAccounts->leftjoin('chart_of_account_types', 'chart_of_account_types.id', 'chart_of_accounts.type');
                $incomeSubAccounts->where('chart_of_account_types.name', 'income');
                $incomeSubAccounts->where('chart_of_accounts.parent', '!=', 0);
                $incomeSubAccounts->where('chart_of_accounts.created_by', \Auth::user()->creatorId());
                $incomeSubAccounts = $incomeSubAccounts->get()->toArray();


                $expenseChartAccounts = ChartOfAccount::select(\DB::raw('CONCAT(chart_of_accounts.code, " - ", chart_of_accounts.name) AS code_name, chart_of_accounts.id as id'))
                    ->leftjoin('chart_of_account_types', 'chart_of_account_types.id', 'chart_of_accounts.type')
                    ->whereIn('chart_of_account_types.name', ['Expenses', 'Costs of Goods Sold'])
                    ->where('chart_of_accounts.created_by', \Auth::user()->creatorId())->get()
                    ->pluck('code_name', 'id');
                $expenseChartAccounts->prepend('Select Account', '');

                $expenseSubAccounts = ChartOfAccount::select(\DB::raw('CONCAT(chart_of_accounts.code, " - ", chart_of_accounts.name) AS code_name,chart_of_accounts.id, chart_of_accounts.code, chart_of_account_parents.account'));
                $expenseSubAccounts->leftjoin('chart_of_account_parents', 'chart_of_accounts.parent', 'chart_of_account_parents.id');
                $expenseSubAccounts->leftjoin('chart_of_account_types', 'chart_of_account_types.id', 'chart_of_accounts.type');
                $expenseSubAccounts->whereIn('chart_of_account_types.name', ['Expenses', 'Costs of Goods Sold']);
                $expenseSubAccounts->where('chart_of_accounts.parent', '!=', 0);
                $expenseSubAccounts->where('chart_of_accounts.created_by', \Auth::user()->creatorId());
                $expenseSubAccounts = $expenseSubAccounts->get()->toArray();

                return view('productservice.edit', compact('category', 'unit', 'tax', 'productService', 'customFields', 'incomeChartAccounts', 'expenseChartAccounts', 'incomeSubAccounts', 'expenseSubAccounts'));
            } else {
                return response()->json(['error' => __('Permission denied.')], 401);
            }
        } else {
            return response()->json(['error' => __('Permission denied.')], 401);
        }
    }

    public function update(Request $request, $id)
    {

        if (\Auth::user()->can('edit product & service')) {
            $productService = ProductService::find($id);
            if ($productService->created_by == \Auth::user()->creatorId()) {
                $rules = [
                    'name' => 'required',
                    'sku' => 'required', Rule::unique('product_services')->ignore($productService->id),
                    'sale_price' => 'required|numeric',
                    'purchase_price' => 'required|numeric',
                    'category_id' => 'required',
                    'unit_id' => 'required',
                    'type' => 'required',

                ];

                $validator = \Validator::make($request->all(), $rules);

                if ($validator->fails()) {
                    $messages = $validator->getMessageBag();

                    return redirect()->route('productservice.index')->with('error', $messages->first());
                }

                $productService->name           = $request->name;
                $productService->description    = $request->description;
                $productService->sku            = $request->sku;
                $productService->sale_price     = $request->sale_price;
                $productService->purchase_price = $request->purchase_price;
                $productService->tax_id         = !empty($request->tax_id) ? implode(',', $request->tax_id) : '';
                $productService->unit_id        = $request->unit_id;

                if (!empty($request->quantity)) {
                    $productService->quantity   = $request->quantity;
                } else {
                    $productService->quantity   = 0;
                }
                $productService->type                       = $request->type;
                $productService->sale_chartaccount_id       = $request->sale_chartaccount_id;
                $productService->expense_chartaccount_id    = $request->expense_chartaccount_id;
                $productService->category_id                = $request->category_id;

                if (!empty($request->pro_image)) {
                    //storage limit
                    $file_path = '/uploads/pro_image/' . $productService->pro_image;
                    $image_size = $request->file('pro_image')->getSize();
                    $result = Utility::updateStorageLimit(\Auth::user()->creatorId(), $image_size);
                    if ($result == 1) {
                        if ($productService->pro_image) {
                            Utility::changeStorageLimit(\Auth::user()->creatorId(), $file_path);
                            $path = storage_path('uploads/pro_image' . $productService->pro_image);
                            //                            if(file_exists($path))
                            //                            {
                            //                                \File::delete($path);
                            //                            }
                        }
                        $fileName = $request->pro_image->getClientOriginalName();
                        $productService->pro_image = $fileName;
                        $dir        = 'uploads/pro_image';
                        $path = Utility::upload_file($request, 'pro_image', $fileName, $dir, []);
                    }
                }

                $productService->created_by     = \Auth::user()->creatorId();
                $productService->save();
                CustomField::saveData($productService, $request->customField);

                return redirect()->route('productservice.index')->with('success', __('Product successfully updated.'));
            } else {
                return redirect()->back()->with('error', __('Permission denied.'));
            }
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function destroy($id)
    {
        if (\Auth::user()->can('delete product & service')) {
            $productService = ProductService::find($id);
            if ($productService->created_by == \Auth::user()->creatorId()) {
                if (!empty($productService->pro_image)) {
                    //storage limit
                    $file_path = '/uploads/pro_image/' . $productService->pro_image;
                    $result = Utility::changeStorageLimit(\Auth::user()->creatorId(), $file_path);
                }

                $productService->delete();

                return redirect()->route('productservice.index')->with('success', __('Product successfully deleted.'));
            } else {
                return redirect()->back()->with('error', __('Permission denied.'));
            }
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function export()
    {
        $name = 'product_service_' . date('Y-m-d i:h:s');
        $data = Excel::download(new ProductServiceExport(), $name . '.xlsx');

        return $data;
    }

    public function importFile()
    {
        return view('productservice.import');
    }

    // public function import(Request $request)
    // {
    //     $rules = [
    //         'file' => 'required|mimes:csv,txt',
    //     ];

    //     $validator = \Validator::make($request->all(), $rules);

    //     if ($validator->fails()) {
    //         $messages = $validator->getMessageBag();

    //         return redirect()->back()->with('error', $messages->first());
    //     }
    //     try {
    //         $products     = (new ProductServiceImport)->toArray(request()->file('file'))[0];
    //         $totalProduct = count($products) - 1;
    //         $errorArray   = [];
    //         for ($i = 1; $i <= count($products) - 1; $i++) {
    //             $items  = $products[$i];

    //             $taxes     = explode(';', $items[5]);

    //             $taxesData = [];
    //             foreach ($taxes as $tax) {
    //                 $taxes       = Tax::where('id', $tax)->first();
    //                 //                $taxesData[] = $taxes->id;
    //                 $taxesData[] = !empty($taxes->id) ? $taxes->id : 0;
    //             }

    //             $taxData = implode(',', $taxesData);
    //             //            dd($taxData);

    //             if (!empty($productBySku)) {
    //                 $productService = $productBySku;
    //             } else {
    //                 $productService = new ProductService();
    //             }

    //             $productService->name           = $items[0];
    //             $productService->sku            = $items[1];
    //             $productService->sale_price     = $items[2];
    //             $productService->purchase_price = $items[3];
    //             $productService->quantity       = $items[4];
    //             $productService->tax_id         = $items[5];
    //             $productService->category_id    = $items[6];
    //             $productService->unit_id        = $items[7];
    //             $productService->type           = $items[8];
    //             $productService->description    = $items[9];
    //             $productService->created_by     = \Auth::user()->creatorId();

    //             if (empty($productService)) {
    //                 $errorArray[] = $productService;
    //             } else {
    //                 $productService->save();
    //             }
    //         }
    //     } catch (\Throwable $th) {
    //         return redirect()->back()->with('error', 'Something went wrong, Please try again');
    //     }

    //     $errorRecord = [];
    //     if (empty($errorArray)) {

    //         $data['status'] = 'success';
    //         $data['msg']    = __('Record successfully imported');
    //     } else {
    //         $data['status'] = 'error';
    //         $data['msg']    = count($errorArray) . ' ' . __('Record imported fail out of' . ' ' . $totalProduct . ' ' . 'record');


    //         foreach ($errorArray as $errorData) {

    //             $errorRecord[] = implode(',', $errorData);
    //         }

    //         \Session::put('errorArray', $errorRecord);
    //     }

    //     return redirect()->back()->with($data['status'], $data['msg']);
    // }

    public function productserviceImportdata(Request $request)
    {
        session_start();
        $html = '<h3 class="text-danger text-center">Below data is not inserted</h3></br>';
        $flag = 0;
        $html .= '<table class="table table-bordered"><tr>';
        try {
            $request = $request->data;
            $file_data = $_SESSION['file_data'];

            unset($_SESSION['file_data']);
        } catch (\Throwable $th) {
            $html = '<h3 class="text-danger text-center">Something went wrong, Please try again</h3></br>';
            return response()->json([
                'html' => true,
                'response' => $html,
            ]);
        }

        foreach ($file_data as $key => $row) {

            try {
                $productService = new ProductService();
                $productService->name = $row[$request['name']];
                $productService->sku = $row[$request['sku']];
                $productService->sale_price = $row[$request['sale_price']];
                $productService->purchase_price = $row[$request['purchase_price']];
                $productService->quantity = (isset($row[$request['type']]) && $row[$request['type']] == 'product') ? $row[$request['quantity']] : 0;
                $productService->type = $row[$request['type']];
                $productService->description = $row[$request['description']];
                $productService->created_by = \Auth::user()->creatorId();
                $productService->save();

            } catch (\Exception $e) {
                $flag = 1;
                $html .= '<tr>';

                $html .= '<td>' . (isset($row[$request['name']]) ? $row[$request['name']] : '-') . '</td>';
                $html .= '<td>' . (isset($row[$request['sku']]) ? $row[$request['sku']] : '-') . '</td>';
                $html .= '<td>' . (isset($row[$request['sale_price']]) ? $row[$request['sale_price']] : '-') . '</td>';
                $html .= '<td>' . (isset($row[$request['purchase_price']]) ? $row[$request['purchase_price']] : '-') . '</td>';
                $html .= '<td>' . (isset($row[$request['quantity']]) ? $row[$request['quantity']] : '-') . '</td>';
                $html .= '<td>' . (isset($row[$request['tax_id']]) ? $row[$request['tax_id']] : '-') . '</td>';
                $html .= '<td>' . (isset($row[$request['category_id']]) ? $row[$request['category_id']] : '-') . '</td>';
                $html .= '<td>' . (isset($row[$request['unit_id']]) ? $row[$request['unit_id']] : '-') . '</td>';
                $html .= '<td>' . (isset($row[$request['type']]) ? $row[$request['type']] : '-') . '</td>';
                $html .= '<td>' . (isset($row[$request['description']]) ? $row[$request['description']] : '-') . '</td>';

                $html .= '</tr>';
            }
        }

        $html .= '
                </table>
                <br />
                ';

        if ($flag == 1) {

            return response()->json([
                'html' => true,
                'response' => $html,
            ]);
        } else {
            return response()->json([
                'html' => false,
                'response' => 'Data Imported Successfully',
            ]);
        }
    }

    public function warehouseDetail($id)
    {
        $products = WarehouseProduct::with(['warehouse'])->where('product_id', '=', $id)->where('created_by', '=', \Auth::user()->creatorId())->get();
        return view('productservice.detail', compact('products'));
    }

    /**
     * Financial Operations - Product Management
     */
    public function financialIndex()
    {
        if (\Auth::user()->can('manage product & service')) {
            $productServices = ProductService::where('created_by', '=', \Auth::user()->creatorId())
                ->with(['category', 'unit', 'taxes'])
                ->get();

            $category = ProductServiceCategory::where('created_by', '=', \Auth::user()->creatorId())
                ->where('type', '=', 'product & service')
                ->get()
                ->pluck('name', 'id');
            $category->prepend('Select Category', '');

            $units = ProductServiceUnit::where('created_by', '=', \Auth::user()->creatorId())
                ->get()
                ->pluck('name', 'id');

            // Get chart accounts for import
            $incomeChartAccounts = ChartOfAccount::select(\DB::raw('CONCAT(chart_of_accounts.code, " - ", chart_of_accounts.name) AS code_name, chart_of_accounts.id as id'))
                ->leftjoin('chart_of_account_types', 'chart_of_account_types.id', 'chart_of_accounts.type')
                ->where('chart_of_account_types.name', 'income')
                ->where('chart_of_accounts.created_by', \Auth::user()->creatorId())
                ->get()
                ->pluck('code_name', 'id');

            $expenseChartAccounts = ChartOfAccount::select(\DB::raw('CONCAT(chart_of_accounts.code, " - ", chart_of_accounts.name) AS code_name, chart_of_accounts.id as id'))
                ->leftjoin('chart_of_account_types', 'chart_of_account_types.id', 'chart_of_accounts.type')
                ->whereIn('chart_of_account_types.name', ['Expenses', 'Costs of Goods Sold'])
                ->where('chart_of_accounts.created_by', \Auth::user()->creatorId())
                ->get()
                ->pluck('code_name', 'id');

            return view('productservice.financial_index', compact(
                'productServices',
                'category',
                'units',
                'incomeChartAccounts',
                'expenseChartAccounts'
            ));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    /**
     * Update product inline from financial management
     */
    public function financialUpdate(Request $request, $id)
    {
        if (\Auth::user()->can('edit product & service')) {
            $productService = ProductService::find($id);

            if ($productService && $productService->created_by == \Auth::user()->creatorId()) {
                $field = $request->field;
                $value = $request->value;

                // Validate based on field type
                if (in_array($field, ['sale_price', 'purchase_price']) && !is_numeric($value)) {
                    return response()->json(['success' => false, 'message' => 'Price must be numeric']);
                }

                if ($field == 'sku') {
                    $exists = ProductService::where('sku', $value)
                        ->where('created_by', \Auth::user()->creatorId())
                        ->where('id', '!=', $id)
                        ->exists();

                    if ($exists) {
                        return response()->json(['success' => false, 'message' => 'SKU already exists']);
                    }
                }

                $productService->$field = $value;
                $productService->save();

                return response()->json(['success' => true, 'message' => 'Product updated successfully']);
            }
        }

        return response()->json(['success' => false, 'message' => 'Permission denied']);
    }

    /**
     * Delete product from financial management
     */
    public function financialDestroy($id)
    {
        if (\Auth::user()->can('delete product & service')) {
            $productService = ProductService::find($id);

            if ($productService && $productService->created_by == \Auth::user()->creatorId()) {
                // Delete image if exists
                if (!empty($productService->pro_image)) {
                    $file_path = '/uploads/pro_image/' . $productService->pro_image;
                    Utility::changeStorageLimit(\Auth::user()->creatorId(), $file_path);
                }

                $productService->delete();
                return response()->json(['success' => true, 'message' => 'Product deleted successfully']);
            }
        }

        return response()->json(['success' => false, 'message' => 'Permission denied']);
    }

    /**
     * Store a new product via financial operations
     */
    public function financialStore(Request $request)
    {
        if (!\Auth::user()->can('create product & service')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        try {
            // Enhanced validation rules
            $rules = [
                'name' => 'required|string|max:255',
                'sku' => [
                    'required',
                    'string',
                    'max:255',
                    Rule::unique('product_services')->where(function ($query) {
                        return $query->where('created_by', \Auth::user()->creatorId());
                    })
                ],
                'sale_price' => 'required|numeric|min:0',
                'purchase_price' => 'nullable|numeric|min:0',
                'category_id' => 'required|exists:product_service_categories,id',
                'unit_id' => 'required|exists:product_service_units,id',
                'type' => 'required|in:product,service',
                'sale_chartaccount_id' => 'required|exists:chart_of_accounts,id',
                'expense_chartaccount_id' => 'required|exists:chart_of_accounts,id',
                'quantity' => 'nullable|numeric|min:0',
                'tax_id' => 'nullable|exists:taxes,id',
                'description' => 'nullable|string|max:1000',
                'pro_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
            ];

            $validator = \Validator::make($request->all(), $rules);

            if ($validator->fails()) {
                return redirect()->back()->withErrors($validator)->withInput();
            }

            // Handle file upload
            $fileName = null;
            if ($request->hasFile('pro_image')) {
                $filenameWithExt = $request->file('pro_image')->getClientOriginalName();
                $filename = pathinfo($filenameWithExt, PATHINFO_FILENAME);
                $extension = $request->file('pro_image')->getClientOriginalExtension();
                $fileName = $filename . '_' . time() . '.' . $extension;
                $dir = 'uploads/productservice/';
                $path = Utility::upload_file($request, 'pro_image', $fileName, $dir, []);
                if ($path['flag'] == 0) {
                    return redirect()->back()->with('error', __($path['msg']));
                }
                $fileName = $path['url'];
            }

            // Create new product
            $productService = new ProductService();
            $productService->name = $request->name;
            $productService->sku = $request->sku;
            $productService->sale_price = $request->sale_price;
            $productService->purchase_price = $request->purchase_price ?? 0;
            $productService->quantity = $request->type == 'product' ? ($request->quantity ?? 0) : 0;
            $productService->tax_id = $request->tax_id ? implode(',', [$request->tax_id]) : '';
            $productService->category_id = $request->category_id;
            $productService->unit_id = $request->unit_id;
            $productService->type = $request->type;
            $productService->description = $request->description;
            $productService->pro_image = $fileName;
            $productService->sale_chartaccount_id = $request->sale_chartaccount_id;
            $productService->expense_chartaccount_id = $request->expense_chartaccount_id;
            $productService->created_by = \Auth::user()->creatorId();

            $productService->save();

            return redirect()->route('financial.productservice.index')->with('success', __('تم إنشاء المنتج بنجاح.'));

        } catch (\Exception $e) {
            \Log::error('Financial product creation failed', [
                'error' => $e->getMessage(),
                'user' => \Auth::id(),
                'data' => $request->all()
            ]);

            return redirect()->back()->with('error', __('فشل في إنشاء المنتج: ') . $e->getMessage())->withInput();
        }
    }

    /**
     * Show import form for financial operations
     */
    public function showImportForm()
    {
        if (!\Auth::user()->can('create product & service')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $category = ProductServiceCategory::where('created_by', '=', \Auth::user()->creatorId())->where('type', '=', 'product & service')->get()->pluck('name', 'id');
        $category->prepend('Select Category', '');

        $units = ProductServiceUnit::where('created_by', '=', \Auth::user()->creatorId())
            ->get()
            ->pluck('name', 'id');
        $units->prepend('Select Unit', '');

        // Get chart accounts for import
        $incomeChartAccounts = ChartOfAccount::select(\DB::raw('CONCAT(chart_of_accounts.code, " - ", chart_of_accounts.name) AS code_name, chart_of_accounts.id as id'))
            ->leftjoin('chart_of_account_types', 'chart_of_account_types.id', 'chart_of_accounts.type')
            ->where('chart_of_account_types.name', 'income')
            ->where('chart_of_accounts.created_by', \Auth::user()->creatorId())
            ->get()
            ->pluck('code_name', 'id');
        $incomeChartAccounts->prepend('Select Account', '');

        $expenseChartAccounts = ChartOfAccount::select(\DB::raw('CONCAT(chart_of_accounts.code, " - ", chart_of_accounts.name) AS code_name, chart_of_accounts.id as id'))
            ->leftjoin('chart_of_account_types', 'chart_of_account_types.id', 'chart_of_accounts.type')
            ->whereIn('chart_of_account_types.name', ['Expenses', 'Costs of Goods Sold'])
            ->where('chart_of_accounts.created_by', \Auth::user()->creatorId())
            ->get()
            ->pluck('code_name', 'id');
        $expenseChartAccounts->prepend('Select Account', '');

        return view('productservice.financial_import', compact('category', 'units', 'incomeChartAccounts', 'expenseChartAccounts'));
    }

    /**
     * Download import template
     */
    public function downloadTemplate(Request $request)
    {
        $defaultIncomeAccount = $request->default_income_account;
        $defaultExpenseAccount = $request->default_expense_account;
        $defaultCategory = $request->default_category;
        $defaultUnit = $request->default_unit;

        // Create CSV content
        $headers = [
            'Name',
            'SKU',
            'Sale Price (Before VAT)',
            'Purchase Price',
            'Quantity',
            'Tax IDs (comma separated)',
            'Type (product/service)',
            'Description'
        ];

        // Sample data with default values
        $sampleData = [
            [
                'Sample Product 1',
                'PROD001',
                '100.00',
                '80.00',
                '50',
                '1,2',
                'product',
                'Sample product description'
            ],
            [
                'Sample Service 1',
                'SERV001',
                '200.00',
                '150.00',
                '0',
                '1',
                'service',
                'Sample service description'
            ]
        ];

        $filename = 'products_import_template_' . date('Y-m-d') . '.csv';

        $handle = fopen('php://output', 'w');

        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');

        // Add BOM for UTF-8
        fprintf($handle, chr(0xEF).chr(0xBB).chr(0xBF));

        // Write headers
        fputcsv($handle, $headers);

        // Write sample data
        foreach ($sampleData as $row) {
            fputcsv($handle, $row);
        }

        fclose($handle);
        exit;
    }

    /**
     * Import products from CSV
     */
    public function importProducts(Request $request)
    {
        if (!\Auth::user()->can('create product & service')) {
            return response()->json(['success' => false, 'message' => 'Permission denied']);
        }

        try {
            $request->validate([
                'import_file' => 'required|file|mimes:csv,txt',
                'default_income_account' => 'required',
                'default_expense_account' => 'required',
                'default_category' => 'required',
                'default_unit' => 'required',
                'delete_existing' => 'boolean'
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed: ' . implode(', ', $e->validator->errors()->all())
            ]);
        }

        try {
            $file = $request->file('import_file');
            $deleteExisting = $request->boolean('delete_existing');

            // Default values
            $defaultIncomeAccount = $request->default_income_account;
            $defaultExpenseAccount = $request->default_expense_account;
            $defaultCategory = $request->default_category;
            $defaultUnit = $request->default_unit;

            // Handle special case where 0 means create default
            if ($defaultIncomeAccount == '0') {
                $defaultIncomeAccount = null;
            }
            if ($defaultExpenseAccount == '0') {
                $defaultExpenseAccount = null;
            }
            if ($defaultCategory == '0') {
                $defaultCategory = null;
            }
            if ($defaultUnit == '0') {
                $defaultUnit = null;
            }

            // Validate that default values exist or create them if missing
            if (!$defaultIncomeAccount || !ChartOfAccount::find($defaultIncomeAccount)) {
                // Create default income account if not exists
                $incomeAccount = ChartOfAccount::create([
                    'code' => '4000',
                    'name' => 'Sales Revenue',
                    'type' => 4, // Revenue type
                    'sub_type' => 15, // Revenue subtype
                    'is_enabled' => 1,
                    'created_by' => \Auth::user()->creatorId(),
                ]);
                $defaultIncomeAccount = $incomeAccount->id;
            }

            if (!$defaultExpenseAccount || !ChartOfAccount::find($defaultExpenseAccount)) {
                // Create default expense account if not exists
                $expenseAccount = ChartOfAccount::create([
                    'code' => '5000',
                    'name' => 'Cost of Goods Sold',
                    'type' => 5, // Expense type
                    'sub_type' => 16, // Expense subtype
                    'is_enabled' => 1,
                    'created_by' => \Auth::user()->creatorId(),
                ]);
                $defaultExpenseAccount = $expenseAccount->id;
            }

            if (!$defaultCategory || !ProductServiceCategory::find($defaultCategory)) {
                // Create default category if not exists
                $category = ProductServiceCategory::create([
                    'name' => 'General',
                    'type' => 'product & service',
                    'color' => '#007bff',
                    'created_by' => \Auth::user()->creatorId(),
                ]);
                $defaultCategory = $category->id;
            }

            if (!$defaultUnit || !ProductServiceUnit::find($defaultUnit)) {
                // Create default unit if not exists
                $unit = ProductServiceUnit::create([
                    'name' => 'Piece',
                    'created_by' => \Auth::user()->creatorId(),
                ]);
                $defaultUnit = $unit->id;
            }

            // Delete existing products if requested
            if ($deleteExisting) {
                $deletedCount = ProductService::where('created_by', \Auth::user()->creatorId())->count();
                ProductService::where('created_by', \Auth::user()->creatorId())->delete();
            }

            // Read CSV file using fgetcsv for better handling
            $filePath = $file->getPathname();
            $handle = fopen($filePath, 'r');

            if (!$handle) {
                return response()->json(['success' => false, 'message' => 'Cannot open the uploaded file']);
            }

            // Read all CSV data
            $csvData = [];
            while (($row = fgetcsv($handle)) !== FALSE) {
                $csvData[] = $row;
            }
            fclose($handle);

            if (empty($csvData)) {
                return response()->json(['success' => false, 'message' => 'CSV file is empty or invalid']);
            }

            // Remove header row
            $headers = array_shift($csvData);

            // Debug: Log the first few rows
            \Log::info('CSV Import Debug', [
                'headers' => $headers,
                'first_row' => isset($csvData[0]) ? $csvData[0] : 'No data rows',
                'total_rows' => count($csvData)
            ]);

            $successCount = 0;
            $errorCount = 0;
            $errors = [];

            foreach ($csvData as $rowIndex => $row) {
                try {
                    // Skip empty rows
                    if (empty(array_filter($row))) {
                        continue;
                    }

                    // Ensure we have at least 8 columns
                    while (count($row) < 8) {
                        $row[] = '';
                    }

                    // Clean and trim data
                    $productData = [
                        'name' => trim($row[0] ?? ''),
                        'sku' => trim($row[1] ?? ''),
                        'sale_price' => is_numeric($row[2] ?? 0) ? floatval($row[2]) : 0,
                        'purchase_price' => is_numeric($row[3] ?? 0) ? floatval($row[3]) : 0,
                        'quantity' => is_numeric($row[4] ?? 0) ? floatval($row[4]) : 0,
                        'tax_id' => trim($row[5] ?? '') ?: null, // Set to null if empty
                        'type' => strtolower(trim($row[6] ?? 'product')),
                        'description' => trim($row[7] ?? ''),
                    ];

                    // Validate type
                    if (!in_array($productData['type'], ['product', 'service'])) {
                        $productData['type'] = 'product';
                    }

                    // Debug log for each row
                    \Log::info('Processing row ' . ($rowIndex + 1), $productData);

                    // Validate required fields
                    if (empty($productData['name']) || empty($productData['sku'])) {
                        $errors[] = "Row " . ($rowIndex + 2) . ": Name and SKU are required. Name: '{$productData['name']}', SKU: '{$productData['sku']}'";
                        $errorCount++;
                        continue;
                    }

                    // Check for duplicate SKU
                    $existingSku = ProductService::where('sku', $productData['sku'])
                        ->where('created_by', \Auth::user()->creatorId())
                        ->exists();

                    if ($existingSku) {
                        $errors[] = "Row " . ($rowIndex + 2) . ": SKU '{$productData['sku']}' already exists";
                        $errorCount++;
                        continue;
                    }

                    // Create new product
                    $productService = new ProductService();
                    $productService->name = $productData['name'];
                    $productService->sku = $productData['sku'];
                    $productService->sale_price = $productData['sale_price'];
                    $productService->purchase_price = $productData['purchase_price'];
                    $productService->quantity = $productData['type'] == 'product' ? $productData['quantity'] : 0;
                    $productService->tax_id = $productData['tax_id'];
                    $productService->type = $productData['type'];
                    $productService->description = $productData['description'];
                    $productService->category_id = $defaultCategory;
                    $productService->unit_id = $defaultUnit;
                    $productService->sale_chartaccount_id = $defaultIncomeAccount;
                    $productService->expense_chartaccount_id = $defaultExpenseAccount;
                    $productService->created_by = \Auth::user()->creatorId();

                    // Save and log success
                    if ($productService->save()) {
                        $successCount++;
                        \Log::info('Product saved successfully', ['id' => $productService->id, 'name' => $productService->name]);
                    } else {
                        $errors[] = "Row " . ($rowIndex + 2) . ": Failed to save product";
                        $errorCount++;
                    }

                } catch (\Exception $e) {
                    $errorMessage = "Row " . ($rowIndex + 2) . ": " . $e->getMessage();
                    $errors[] = $errorMessage;
                    $errorCount++;
                    \Log::error('Import error', ['row' => $rowIndex + 2, 'error' => $e->getMessage(), 'data' => $row]);
                }
            }

            // Log final results
            \Log::info('Import completed', [
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'total_rows_processed' => count($csvData)
            ]);

            $message = "Import completed. Success: {$successCount}, Errors: {$errorCount}";

            // If no products were processed at all, it might be a format issue
            if ($successCount == 0 && $errorCount == 0) {
                $message = "No valid data found in CSV file. Please check the file format.";
            }

            return response()->json([
                'success' => $successCount > 0 || $errorCount == 0,
                'message' => $message,
                'details' => [
                    'success_count' => $successCount,
                    'error_count' => $errorCount,
                    'total_rows' => count($csvData),
                    'errors' => $errors,
                    'debug_info' => [
                        'headers_found' => $headers,
                        'first_row_sample' => isset($csvData[0]) ? $csvData[0] : null
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Import exception', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return response()->json([
                'success' => false,
                'message' => 'Import failed: ' . $e->getMessage(),
                'debug' => [
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]
            ]);
        }
    }

    /**
     * Export products to CSV
     */
    public function exportProducts()
    {
        if (!\Auth::user()->can('manage product & service')) {
            return response()->json(['success' => false, 'message' => 'Permission denied']);
        }

        $products = ProductService::where('created_by', \Auth::user()->creatorId())
            ->with(['category', 'unit'])
            ->get();

        $filename = 'products_export_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'ID',
            'Name',
            'SKU',
            'Sale Price',
            'Purchase Price',
            'Quantity',
            'Tax IDs',
            'Category',
            'Unit',
            'Type',
            'Description',
            'Created At'
        ];

        $handle = fopen('php://output', 'w');

        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');

        // Add BOM for UTF-8
        fprintf($handle, chr(0xEF).chr(0xBB).chr(0xBF));

        // Write headers
        fputcsv($handle, $headers);

        // Write data
        foreach ($products as $product) {
            $row = [
                $product->id,
                $product->name,
                $product->sku,
                $product->sale_price,
                $product->purchase_price,
                $product->quantity,
                $product->tax_id,
                $product->category ? $product->category->name : '',
                $product->unit ? $product->unit->name : '',
                $product->type,
                $product->description,
                $product->created_at->format('Y-m-d H:i:s')
            ];
            fputcsv($handle, $row);
        }

        fclose($handle);
        exit;
    }

    public function searchProducts(Request $request)
    {
        $lastsegment = $request->session_key;

        if (Auth::user()->can('manage pos') && $request->ajax() && isset($lastsegment) && !empty($lastsegment)) {

            $output = "";

            // تحديد المستودع المستخدم
            $warehouse_id = $request->war_id != '0' ? $request->war_id : 1;

            // الحصول على معرفات المنتجات في المستودع المحدد
            $ids = WarehouseProduct::where('warehouse_id', $warehouse_id)->get()->pluck('product_id')->toArray();

            // بناء الاستعلام الأساسي
            $query = ProductService::getallproducts()->whereIn('product_services.id', $ids)->with(['unit']);

            // إضافة شروط البحث
            if ($request->search != '') {
                // البحث بالنص (اسم أو باركود)
                $query->where('product_services.'.$request->type, 'LIKE', "%{$request->search}%");
            }

            // إضافة شرط الفئة إذا كان محدد
            if ($request->cat_id !== '' && $request->cat_id != '0') {
                $query->where('category_id', $request->cat_id);
            }

            $products = $query->get();

            if (count($products)>0)
            {
                foreach ($products as $key => $product)
                {
                    // استخدام نفس المستودع المحدد في البحث
                    $quantity = $product->warehouseProduct($product->id, $warehouse_id);

                    // تخطي المنتجات التي لا تحتوي على كمية في المستودع المحدد
                    if ($quantity <= 0) {
                        continue;
                    }

                    $unit = (!empty($product) && !empty($product->unit)) ? $product->unit->name : '';

                        if (!empty($product->pro_image)) {
                            $image_url = ('uploads/pro_image') . '/' . $product->pro_image;
                        } else {
                            $image_url = ('uploads/pro_image') . '/default.png';
                        }
                        if ($request->session_key == 'purchases') {
                            $productprice = $product->purchase_price != 0 ? $product->purchase_price : 0;
                        } else if ($request->session_key == 'pos') {
                            $productprice = $product->sale_price != 0 ? $product->sale_price : 0;
                        } else {
                            $productprice = $product->sale_price != 0 ? $product->sale_price : $product->purchase_price;
                        }

                        $output .= '

                                    <div class="col-lg-2 col-md-2 col-sm-3 col-xs-4 col-12">
                                        <div class="tab-pane fade show active toacart w-100" data-url="' . url('add-to-cart/' . $product->id . '/' . $lastsegment) . '">
                                            <div class="position-relative card">
                                                <img alt="Image placeholder" src="' . asset(Storage::url($image_url)) . '" class="card-image avatar shadow hover-shadow-lg" style=" height: 6rem; width: 100%;">
                                                <div class="p-0 custom-card-body card-body d-flex ">
                                                    <div class="card-body my-2 p-2 text-left card-bottom-content">
                                                        <h6 class="mb-2 text-dark product-title-name">' . $product->name . '</h6>
                                                        <h6 class="mb-2 text-dark product-title-name">' . $product->sku . '</h6>
                                                        <small class="badge badge-primary mb-0">' . Auth::user()->priceFormat($productprice) . '</small>

                                                        <small class="top-badge badge badge-danger mb-0">' . $quantity . ' ' . $unit . '</small>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                            ';

                }

                // إذا لم يتم إنشاء أي output (جميع المنتجات بكمية صفر)
                if (empty($output)) {
                    $output='<div class="card card-body col-12 text-center">
                        <h5>'.__("No Product Available in Selected Warehouse").'</h5>
                        </div>';
                }

                return Response($output);
            } else {
                $output='<div class="card card-body col-12 text-center">
                    <h5>'.__("No Product Available").'</h5>
                    </div>';
                return Response($output);
            }
        }
    }

    public function addToCart(Request $request, $id, $session_key)
    {

        if (Auth::user()->can('manage product & service') && $request->ajax()) {
            $product = ProductService::find($id);
            $productquantity = 0;

            if ($product) {
                $productquantity = $product->getTotalProductQuantity();
            }

            // التحقق من وجود المنتج وحالة المخزون
            if (!$product) {
                return response()->json(
                    [
                        'code' => 404,
                        'status' => 'Error',
                        'error' => __('Product not found!'),
                    ],
                    404
                );
            }

            // التحقق من المخزون فقط للمنتجات وليس الخدمات
            if ($session_key == 'pos' && $product->type == 'product' && $productquantity == 0) {
                return response()->json(
                    [
                        'code' => 404,
                        'status' => 'Error',
                        'error' => __('This product is out of stock!'),
                    ],
                    404
                );
            }

            $productname = $product->name;

            if ($session_key == 'purchases') {

                $productprice = $product->purchase_price != 0 ? $product->purchase_price : 0;
            } else if ($session_key == 'pos') {

                $productprice = $product->sale_price != 0 ? $product->sale_price : 0;
            } else {

                $productprice = $product->sale_price != 0 ? $product->sale_price : $product->purchase_price;
            }

            $originalquantity = (int)$productquantity;

            $taxes = Utility::tax($product->tax_id);

            $totalTaxRate = Utility::totalTaxRate($product->tax_id);

            $product_tax = '';
            $product_tax_id = [];
            foreach ($taxes as $tax) {
                $product_tax .= !empty($tax) ? "<span class='badge badge-primary'>" . $tax->name . ' (' . $tax->rate . '%)' . "</span><br>" : '';
                $product_tax_id[] = !empty($tax) ? $tax->id : 0;
            }

            if (empty($product_tax)) {
                $product_tax = "-";
            }
            $producttax = $totalTaxRate;


            $tax = ($productprice * $producttax) / 100;

            // حساب المجموع الفرعي (السعر × الكمية)
            $subtotal        = $productprice;
            // المجموع الكلي (المجموع الفرعي + الضريبة)
            $total = $subtotal + $tax;
            $cart = session()->get($session_key);
            $image_url = (!empty($product->pro_image) && Storage::exists($product->pro_image)) ? $product->pro_image : 'uploads/pro_image/' . $product->pro_image;

            $model_delete_id = 'delete-form-' . $id;

            $carthtml = '';

            $carthtml .= '<tr data-product-id="' . $id . '" id="product-id-' . $id . '">
                            <td class="cart-images">
                                <img alt="Image placeholder" src="' . asset(Storage::url($image_url)) . '" class="card-image avatar shadow hover-shadow-lg">
                            </td>

                            <td class="name">' . $productname . '</td>

                            <td class="">
                                   <span class="quantity buttons_added">
                                         <input type="button" value="-" class="minus">
                                         <input type="number" step="1" min="1" max="" name="quantity" title="' . __('Quantity') . '" class="input-number" size="4" data-url="' . url('update-cart/') . '" data-id="' . $id . '">
                                         <input type="button" value="+" class="plus">
                                   </span>
                            </td>


                            <td class="tax">' . $product_tax . '</td>

                            <td class="price">' . Auth::user()->priceFormat($productprice) . '</td>

                            <td class="subtotal">' . Auth::user()->priceFormat($subtotal) . '</td>

                            <td class="action-btn mt-3">
                                 <a href="#" class="btn btn-sm bg-danger bs-pass-para-pos" data-confirm="' . __("Are You Sure?") . '" data-text="' . __("This action can not be undone. Do you want to continue?") . '" data-confirm-yes="' . $model_delete_id . '" title="' . __('Delete') . '" data-id="' . $id . '">
                                   <span class=""><i class="ti ti-trash text-white"></i></span>
                                 </a>
                                 <form method="post" action="' . url('remove-from-cart') . '"  accept-charset="UTF-8" id="' . $model_delete_id . '">
                                      <input name="_method" type="hidden" value="DELETE">
                                      <input name="_token" type="hidden" value="' . csrf_token() . '">
                                      <input type="hidden" name="session_key" value="' . $session_key . '">
                                      <input type="hidden" name="id" value="' . $id . '">
                                 </form>

                            </td>
                        </tr>';

            // if cart is empty then this the first product
            if (!$cart) {
                // حساب المجموع الفرعي (السعر × الكمية)
                $itemSubtotal = $productprice * 1; // الكمية هي 1
                // حساب الضريبة
                $itemTax = ($itemSubtotal * $producttax) / 100;
                // المجموع الكلي (المجموع الفرعي + الضريبة)
                $itemTotal = $itemSubtotal + $itemTax;

                $cart = [
                    $id => [
                        "name" => $productname,
                        "quantity" => 1,
                        "price" => $productprice,
                        "id" => $id,
                        "tax" => $producttax,
                        "subtotal" => $itemTotal, // المجموع الكلي بعد الضريبة
                        "originalquantity" => $originalquantity,
                        "product_tax" => $product_tax,
                        "product_tax_id" => !empty($product_tax_id) ? implode(',', $product_tax_id) : 0,
                    ],
                ];


                if ($originalquantity < $cart[$id]['quantity'] && $session_key == 'pos') {
                    return response()->json(
                        [
                            'code' => 404,
                            'status' => 'Error',
                            'error' => __('This product is out of stock!'),
                        ],
                        404
                    );
                }

                session()->put($session_key, $cart);

                return response()->json(
                    [
                        'code' => 200,
                        'status' => 'Success',
                        'success' => $productname . __(' added to cart successfully!'),
                        'product' => $cart[$id],
                        'carthtml' => $carthtml,
                    ]
                );
            }

            // if cart not empty then check if this product exist then add quantity 1 (not increment)
            if (isset($cart[$id])) {

                $cart[$id]['quantity'] += 1; // إضافة كمية 1 فقط
                $cart[$id]['id'] = $id;

                // حساب المجموع الفرعي (السعر × الكمية)
                $subtotal = $cart[$id]["price"] * $cart[$id]["quantity"];
                // حساب الضريبة
                $tax = ($subtotal * $cart[$id]["tax"]) / 100;
                // المجموع الكلي (المجموع الفرعي + الضريبة)
                $total = $subtotal + $tax;

                $cart[$id]["subtotal"] = $total; // المجموع الكلي بعد الضريبة
                $cart[$id]["originalquantity"] = $originalquantity;

                if ($originalquantity < $cart[$id]['quantity'] && $session_key == 'pos') {
                    return response()->json(
                        [
                            'code' => 404,
                            'status' => 'Error',
                            'error' => __('This product is out of stock!'),
                        ],
                        404
                    );
                }

                session()->put($session_key, $cart);

                return response()->json(
                    [
                        'code' => 200,
                        'status' => 'Success',
                        'success' => $productname . __(' added to cart successfully!'),
                        'product' => $cart[$id],
                        'carttotal' => $cart,
                    ]
                );
            }

            // if item not exist in cart then add to cart with quantity = 1
            // حساب المجموع الفرعي (السعر × الكمية)
            $itemSubtotal = $productprice * 1; // الكمية هي 1
            // حساب الضريبة
            $itemTax = ($itemSubtotal * $producttax) / 100;
            // المجموع الكلي (المجموع الفرعي + الضريبة)
            $itemTotal = $itemSubtotal + $itemTax;

            $cart[$id] = [
                "name" => $productname,
                "quantity" => 1,
                "price" => $productprice,
                "tax" => $producttax,
                "subtotal" => $itemTotal, // المجموع الكلي بعد الضريبة
                "id" => $id,
                "originalquantity" => $originalquantity,
                "product_tax" => $product_tax,
            ];

            // التحقق من المخزون فقط للمنتجات وليس الخدمات
            if ($originalquantity < $cart[$id]['quantity'] && $session_key == 'pos' && $product->type == 'product') {
                return response()->json(
                    [
                        'code' => 404,
                        'status' => 'Error',
                        'error' => __('This product is out of stock!'),
                    ],
                    404
                );
            }

            session()->put($session_key, $cart);

            return response()->json(
                [
                    'code' => 200,
                    'status' => 'Success',
                    'success' => $productname . __(' added to cart successfully!'),
                    'product' => $cart[$id],
                    'carthtml' => $carthtml,
                    'carttotal' => $cart,
                ]
            );
        } else {
            return response()->json(
                [
                    'code' => 404,
                    'status' => 'Error',
                    'error' => __('This Product is not found!'),
                ],
                404
            );
        }
    }

    public function updateCart(Request $request)
    {

        $id          = $request->id;
        $quantity    = $request->quantity;
        $discount    = $request->discount;
        $session_key = $request->session_key;

        if (Auth::user()->can('manage product & service') && $request->ajax() && isset($id) && !empty($id) && isset($session_key) && !empty($session_key)) {
            $cart = session()->get($session_key);


            if (isset($cart[$id]) && $quantity == 0) {
                unset($cart[$id]);
            }

            if ($quantity) {

                $cart[$id]["quantity"] = $quantity;

                $producttax            = isset($cart[$id]) ? $cart[$id]["tax"] : 0;
                $productprice          = $cart[$id]["price"];

                // حساب المجموع الفرعي (السعر × الكمية)
                $subtotal = $productprice * $quantity;
                // حساب الضريبة
                $tax = ($subtotal * $producttax) / 100;
                // المجموع الكلي (المجموع الفرعي + الضريبة)
                $total = $subtotal + $tax;

                $cart[$id]["subtotal"] = $total; // المجموع الكلي بعد الضريبة
            }

            if (isset($cart[$id]) && ($cart[$id]["originalquantity"]) < $cart[$id]['quantity'] && $session_key == 'pos') {
                return response()->json(
                    [
                        'code' => 404,
                        'status' => 'Error',
                        'error' => __('This product is out of stock!'),
                    ],
                    404
                );
            }

            $subtotal = array_sum(array_column($cart, 'subtotal'));
            $discount = $request->discount;
            $total = $subtotal - $discount;

            $totalDiscount = Auth::user()->priceFormat($total);
            $discount = $totalDiscount;


            session()->put($session_key, $cart);

            return response()->json(
                [
                    'code' => 200,
                    'success' => __('Cart updated successfully!'),
                    'product' => $cart,
                    'discount' => $discount,
                ]
            );
        } else {
            return response()->json(
                [
                    'code' => 404,
                    'status' => 'Error',
                    'error' => __('This Product is not found!'),
                ],
                404
            );
        }
    }

    public function emptyCart(Request $request)
    {
        $session_key = $request->session_key;

        if (Auth::user()->can('manage product & service') && isset($session_key) && !empty($session_key)) {
            $cart = session()->get($session_key);
            if (isset($cart) && count($cart) > 0) {
                session()->forget($session_key);
            }

            return redirect()->back()->with('error', __('Cart is empty!'));
        } else {
            return redirect()->back()->with('error', __('Cart cannot be empty!.'));
        }
    }

    public function warehouseemptyCart(Request $request)
    {
        $session_key = $request->session_key;

        $cart = session()->get($session_key);
        if (isset($cart) && count($cart) > 0) {
            session()->forget($session_key);
        }

        return response()->json();
    }

    public function removeFromCart(Request $request)
    {
        $id          = $request->id;
        $session_key = $request->session_key;

        // إضافة logging للتشخيص
        \Log::info('removeFromCart called', [
            'id' => $id,
            'session_key' => $session_key,
            'request_data' => $request->all()
        ]);

        if (Auth::user()->can('manage product & service') && isset($id) && !empty($id) && isset($session_key) && !empty($session_key)) {
            $cart = session()->get($session_key);

            \Log::info('Cart before removal', ['cart' => $cart]);

            if (isset($cart[$id])) {
                // حفظ اسم المنتج قبل الحذف للرسالة
                $productName = $cart[$id]['name'] ?? 'Product';

                unset($cart[$id]);
                session()->put($session_key, $cart);

                \Log::info('Product removed successfully', [
                    'removed_id' => $id,
                    'cart_after' => session()->get($session_key)
                ]);

                // إرجاع استجابة JSON للطلبات AJAX
                if ($request->ajax() || $request->wantsJson()) {
                    return response()->json([
                        'code' => 200,
                        'status' => 'Success',
                        'success' => $productName . ' ' . __('removed from cart successfully!'),
                        'remaining_items' => count($cart),
                        'cart' => $cart
                    ]);
                }

                return redirect()->back()->with('success', __('Product removed from cart!'));
            } else {
                \Log::warning('Product not found in cart', ['id' => $id, 'cart_keys' => array_keys($cart ?? [])]);

                // إرجاع استجابة JSON للطلبات AJAX
                if ($request->ajax() || $request->wantsJson()) {
                    return response()->json([
                        'code' => 404,
                        'status' => 'Error',
                        'error' => __('Product not found in cart!')
                    ], 404);
                }

                return redirect()->back()->with('error', __('Product not found in cart!'));
            }
        } else {
            \Log::error('removeFromCart failed validation', [
                'can_manage' => Auth::user()->can('manage product & service'),
                'id_valid' => isset($id) && !empty($id),
                'session_key_valid' => isset($session_key) && !empty($session_key)
            ]);

            // إرجاع استجابة JSON للطلبات AJAX
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'code' => 403,
                    'status' => 'Error',
                    'error' => __('Permission denied or invalid data!')
                ], 403);
            }

            return redirect()->back()->with('error', __('This Product is not found!'));
        }
    }

    /**
     * Get tax rates for AJAX calculation
     */
    public function getTaxRates(Request $request)
    {
        if (\Auth::user()->can('manage product & service') && $request->ajax()) {
            $taxIds = $request->tax_ids;
            $totalRate = 0;

            if (!empty($taxIds) && is_array($taxIds)) {
                $totalRate = Utility::totalTaxRate(implode(',', $taxIds));
            }

            return response()->json([
                'success' => true,
                'total_rate' => $totalRate
            ]);
        }

        return response()->json([
            'success' => false,
            'total_rate' => 0
        ], 403);
    }

    /**
     * Financial Operations - Product Service Management (Copy of Original)
     */
    public function financialProductServiceIndex(Request $request)
    {
        if (\Auth::user()->can('manage product & service')) {
            $category = ProductServiceCategory::where('created_by', '=', \Auth::user()->creatorId())->where('type', '=', 'product & service')->get()->pluck('name', 'id');
            $category->prepend('Select Category', '');

            $units = ProductServiceUnit::where('created_by', '=', \Auth::user()->creatorId())
                ->get()
                ->pluck('name', 'id');

            // Get chart accounts for import
            $incomeChartAccounts = ChartOfAccount::select(\DB::raw('CONCAT(chart_of_accounts.code, " - ", chart_of_accounts.name) AS code_name, chart_of_accounts.id as id'))
                ->leftjoin('chart_of_account_types', 'chart_of_account_types.id', 'chart_of_accounts.type')
                ->where('chart_of_account_types.name', 'income')
                ->where('chart_of_accounts.created_by', \Auth::user()->creatorId())
                ->get()
                ->pluck('code_name', 'id');

            $expenseChartAccounts = ChartOfAccount::select(\DB::raw('CONCAT(chart_of_accounts.code, " - ", chart_of_accounts.name) AS code_name, chart_of_accounts.id as id'))
                ->leftjoin('chart_of_account_types', 'chart_of_account_types.id', 'chart_of_accounts.type')
                ->whereIn('chart_of_account_types.name', ['Expenses', 'Costs of Goods Sold'])
                ->where('chart_of_accounts.created_by', \Auth::user()->creatorId())
                ->get()
                ->pluck('code_name', 'id');

            if (!empty($request->category)) {
                $productServices = ProductService::where('created_by', '=', \Auth::user()->creatorId())->where('category_id', $request->category)->with(['category', 'unit'])->get();
            } else {
                $productServices = ProductService::where('created_by', '=', \Auth::user()->creatorId())->with(['category', 'unit'])->get();
            }

            return view('productservice.financial_index', compact('productServices', 'category', 'units', 'incomeChartAccounts', 'expenseChartAccounts'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    /**
     * Show the form for creating a new product via financial operations
     */
    public function financialCreate()
    {
        if (\Auth::user()->can('create product & service')) {
            $category = ProductServiceCategory::where('created_by', '=', \Auth::user()->creatorId())->where('type', '=', 'product & service')->get()->pluck('name', 'id');
            $category->prepend('Select Category', '');

            $unit = ProductServiceUnit::where('created_by', '=', \Auth::user()->creatorId())->get()->pluck('name', 'id');
            $unit->prepend('Select Unit', '');

            $tax = Tax::where('created_by', '=', \Auth::user()->creatorId())->get()->pluck('name', 'id');
            $tax->prepend('Select Tax', '');

            $incomeChartAccounts = ChartOfAccount::select(\DB::raw('CONCAT(chart_of_accounts.code, " - ", chart_of_accounts.name) AS code_name, chart_of_accounts.id as id'))
                ->leftjoin('chart_of_account_types', 'chart_of_account_types.id', 'chart_of_accounts.type')
                ->where('chart_of_account_types.name', 'income')
                ->where('chart_of_accounts.created_by', \Auth::user()->creatorId())
                ->get()
                ->pluck('code_name', 'id');
            $incomeChartAccounts->prepend('Select Account', '');

            $expenseChartAccounts = ChartOfAccount::select(\DB::raw('CONCAT(chart_of_accounts.code, " - ", chart_of_accounts.name) AS code_name, chart_of_accounts.id as id'))
                ->leftjoin('chart_of_account_types', 'chart_of_account_types.id', 'chart_of_accounts.type')
                ->whereIn('chart_of_account_types.name', ['Expenses', 'Costs of Goods Sold'])
                ->where('chart_of_accounts.created_by', \Auth::user()->creatorId())
                ->get()
                ->pluck('code_name', 'id');
            $expenseChartAccounts->prepend('Select Account', '');

            return view('productservice.financial_create', compact('category', 'unit', 'tax', 'incomeChartAccounts', 'expenseChartAccounts'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }
}
