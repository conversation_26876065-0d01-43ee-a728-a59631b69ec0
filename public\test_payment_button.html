<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار زر الدفع</title>
    <meta name="csrf-token" content="test-token">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .btn-pay {
            background: linear-gradient(135deg, #8B0000 0%, #DC143C 50%, #B22222 100%);
            border: none;
            color: white;
            font-weight: 700;
            font-size: 1.2rem;
            padding: 12px 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(139, 0, 0, 0.4);
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-pay:hover {
            background: linear-gradient(135deg, #DC143C 0%, #8B0000 50%, #DC143C 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(139, 0, 0, 0.6);
            color: white;
        }

        .btn-pay:disabled {
            background: linear-gradient(135deg, #666 0%, #888 100%);
            box-shadow: none;
            transform: none;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h3>اختبار زر الدفع - POS</h3>
                    </div>
                    <div class="card-body">
                        <!-- حقول مخفية للاختبار -->
                        <input type="hidden" id="vc_name_hidden" value="1">
                        <input type="hidden" id="warehouse_name_hidden" value="1">
                        <input type="hidden" id="discount_hidden" value="0">
                        <input type="hidden" id="delivery_user_hidden" value="">
                        <input type="hidden" id="quotation_id" value="0">
                        
                        <!-- جدول السلة للاختبار -->
                        <div class="mb-3">
                            <h5>محتويات السلة:</h5>
                            <table class="table">
                                <tbody id="tbody">
                                    <tr id="product-id-1">
                                        <td>منتج تجريبي</td>
                                        <td>100 ريال</td>
                                        <td><input type="number" name="quantity" value="1"></td>
                                        <td class="subtotal">100</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- زر الدفع -->
                        <div class="text-center">
                            <button id="payment" type="button" class="btn btn-pay rounded" 
                                    data-ajax-popup="true"
                                    data-size="xl" 
                                    data-align="centered" 
                                    data-url="/pos-payment-type"
                                    data-title="POS Invoice">
                                <i class="fas fa-credit-card me-2"></i>دفع
                            </button>
                        </div>
                        
                        <!-- منطقة الرسائل -->
                        <div id="messages" class="mt-3"></div>
                        
                        <!-- منطقة اللوج -->
                        <div class="mt-4">
                            <h5>سجل الأحداث:</h5>
                            <div id="log" class="border p-3" style="height: 200px; overflow-y: auto; background: #f8f9fa;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div class="modal fade" id="commonModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // دالة لإضافة رسالة إلى اللوج
        function addLog(message) {
            var timestamp = new Date().toLocaleTimeString();
            $('#log').append('<div>[' + timestamp + '] ' + message + '</div>');
            $('#log').scrollTop($('#log')[0].scrollHeight);
        }

        // دالة لعرض الرسائل
        function show_toastr(type, message, icon) {
            var alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
            $('#messages').html('<div class="alert ' + alertClass + '">' + message + '</div>');
            addLog('Toast: ' + type + ' - ' + message);
        }

        // معالج النقر على زر الدفع
        $(document).on('click', '#payment', function(e) {
            addLog('Payment button clicked');
            
            // التحقق من وجود منتجات في السلة
            var hasItemsInCart = $('#tbody tr:not(.no-found)').length > 0;
            addLog('Items in cart: ' + hasItemsInCart);
            
            if (!hasItemsInCart) {
                e.preventDefault();
                e.stopPropagation();
                show_toastr('error', 'يرجى إضافة منتجات إلى السلة أولاً!', 'error');
                return false;
            }
            
            addLog('Payment button validation passed');
        });

        // معالج AJAX popup (مبسط للاختبار)
        $(document).on('click', '[data-ajax-popup="true"]', function (e) {
            addLog('AJAX popup handler triggered');
            
            var data = {};
            var title = $(this).data("title");
            var id = $(this).attr("id");
            var url = $(this).data('url');
            
            addLog('Element ID: ' + id);
            addLog('Title: ' + title);
            addLog('URL: ' + url);

            if(id == "payment") {
                // التحقق من اختيار العميل والمستودع
                var customerId = $('#vc_name_hidden').val();
                var warehouseId = $('#warehouse_name_hidden').val();
                
                addLog('Customer ID: ' + customerId);
                addLog('Warehouse ID: ' + warehouseId);
                
                if (!customerId || customerId == '0' || customerId == '') {
                    show_toastr('error', 'يرجى اختيار عميل أولاً!', 'error');
                    return false;
                }

                if (!warehouseId || warehouseId == '0' || warehouseId == '') {
                    show_toastr('error', 'يرجى اختيار مستودع أولاً!', 'error');
                    return false;
                }

                if (!url) {
                    show_toastr('error', 'Payment URL not found', 'error');
                    return false;
                }
            }

            // جمع البيانات
            if ($('#vc_name_hidden').length > 0) {
                data['vc_name'] = $('#vc_name_hidden').val();
            }
            if ($('#delivery_user_hidden').length > 0) {
                data['user_id'] = $('#delivery_user_hidden').val();
            }
            if ($('#warehouse_name_hidden').length > 0) {
                data['warehouse_name'] = $('#warehouse_name_hidden').val();
            }
            if ($('#discount_hidden').length > 0) {
                data['discount'] = $('#discount_hidden').val();
            }
            if ($('#quotation_id').length > 0) {
                data['quotation_id'] = $('#quotation_id').val();
            }

            addLog('Data to send: ' + JSON.stringify(data));

            // محاكاة طلب AJAX
            addLog('Simulating AJAX request to: ' + url);
            
            $("#commonModal .modal-title").html(title);
            $("#commonModal .modal-body").html('<div class="alert alert-info">تم محاكاة طلب AJAX بنجاح!<br>URL: ' + url + '<br>Data: ' + JSON.stringify(data) + '</div>');
            $("#commonModal").modal('show');
            
            addLog('Modal opened successfully');
        });

        // تسجيل تحميل الصفحة
        $(document).ready(function() {
            addLog('Page loaded and ready');
            addLog('jQuery version: ' + $.fn.jquery);
        });
    </script>
</body>
</html>
