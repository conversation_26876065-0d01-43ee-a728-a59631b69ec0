{{ Form::open(['url' => 'financial/productservice/store', 'method' => 'post', 'enctype' => 'multipart/form-data', 'id' => 'financial-product-form']) }}
<div class="modal-body">
    <div class="row">
        <!-- اسم المنتج -->
        <div class="col-md-12">
            <div class="form-group">
                {{ Form::label('name', __('اسم المنتج'), ['class' => 'form-label']) }}
                {{ Form::text('name', '', ['class' => 'form-control', 'required' => 'required', 'placeholder' => __('أدخل اسم المنتج')]) }}
            </div>
        </div>

        <!-- الرمز التعريفي -->
        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('sku', __('الرمز التعريفي (SKU)'), ['class' => 'form-label']) }}
                {{ Form::text('sku', '', ['class' => 'form-control', 'required' => 'required', 'placeholder' => __('أدخل الرمز التعريفي')]) }}
            </div>
        </div>

        <!-- نوع المنتج -->
        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('type', __('النوع'), ['class' => 'form-label']) }}
                <div class="d-flex radio-check">
                    <div class="custom-control custom-radio custom-control-inline">
                        <input type="radio" id="product" value="product" name="type" class="custom-control-input" checked>
                        <label class="custom-control-label form-label" for="product">{{ __('منتج') }}</label>
                    </div>
                    <div class="custom-control custom-radio custom-control-inline">
                        <input type="radio" id="service" value="service" name="type" class="custom-control-input">
                        <label class="custom-control-label form-label" for="service">{{ __('خدمة') }}</label>
                    </div>
                </div>
            </div>
        </div>

        <!-- سعر البيع -->
        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('sale_price', __('سعر البيع'), ['class' => 'form-label']) }}
                {{ Form::number('sale_price', '', ['class' => 'form-control', 'required' => 'required', 'step' => '0.01', 'min' => '0', 'placeholder' => __('0.00')]) }}
            </div>
        </div>

        <!-- سعر الشراء -->
        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('purchase_price', __('سعر الشراء'), ['class' => 'form-label']) }}
                {{ Form::number('purchase_price', '', ['class' => 'form-control', 'step' => '0.01', 'min' => '0', 'placeholder' => __('0.00')]) }}
            </div>
        </div>

        <!-- الكمية -->
        <div class="col-md-6" id="quantity-section">
            <div class="form-group">
                {{ Form::label('quantity', __('الكمية'), ['class' => 'form-label']) }}
                {{ Form::number('quantity', '', ['class' => 'form-control', 'min' => '0', 'placeholder' => __('0')]) }}
            </div>
        </div>

        <!-- الفئة -->
        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('category_id', __('الفئة'), ['class' => 'form-label']) }}
                {{ Form::select('category_id', $category, null, ['class' => 'form-control select', 'required' => 'required']) }}
            </div>
        </div>

        <!-- الوحدة -->
        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('unit_id', __('الوحدة'), ['class' => 'form-label']) }}
                {{ Form::select('unit_id', $unit, null, ['class' => 'form-control select', 'required' => 'required']) }}
            </div>
        </div>

        <!-- الضريبة -->
        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('tax_id', __('الضريبة'), ['class' => 'form-label']) }}
                {{ Form::select('tax_id', $tax, null, ['class' => 'form-control select']) }}
            </div>
        </div>

        <!-- حساب الإيرادات -->
        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('sale_chartaccount_id', __('حساب الإيرادات'), ['class' => 'form-label']) }}
                {{ Form::select('sale_chartaccount_id', $incomeChartAccounts, null, ['class' => 'form-control select', 'required' => 'required']) }}
            </div>
        </div>

        <!-- حساب المصروفات -->
        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('expense_chartaccount_id', __('حساب المصروفات'), ['class' => 'form-label']) }}
                {{ Form::select('expense_chartaccount_id', $expenseChartAccounts, null, ['class' => 'form-control select', 'required' => 'required']) }}
            </div>
        </div>

        <!-- الوصف -->
        <div class="col-md-12">
            <div class="form-group">
                {{ Form::label('description', __('الوصف'), ['class' => 'form-label']) }}
                {{ Form::textarea('description', '', ['class' => 'form-control', 'rows' => '3', 'placeholder' => __('أدخل وصف المنتج (اختياري)')]) }}
            </div>
        </div>

        <!-- صورة المنتج -->
        <div class="col-md-12">
            <div class="form-group">
                {{ Form::label('pro_image', __('صورة المنتج'), ['class' => 'form-label']) }}
                {{ Form::file('pro_image', ['class' => 'form-control', 'accept' => 'image/*']) }}
                <small class="text-muted">{{ __('الحد الأقصى: 2MB، الصيغ المدعومة: JPG, PNG, GIF') }}</small>
            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <input type="button" value="{{ __('إلغاء') }}" class="btn btn-light" data-bs-dismiss="modal">
    <input type="submit" value="{{ __('إنشاء') }}" class="btn btn-primary">
</div>
{{ Form::close() }}

<script>
$(document).ready(function() {
    // Toggle quantity field based on product type
    $('input[name="type"]').on('change', function() {
        if ($(this).val() === 'service') {
            $('#quantity-section').hide();
            $('input[name="quantity"]').val('0');
        } else {
            $('#quantity-section').show();
        }
    });

    // Form submission handling
    $('#financial-product-form').on('submit', function(e) {
        // Basic validation
        var isValid = true;
        var errorMessage = '';

        // Check required fields
        var requiredFields = [
            { name: 'name', label: 'اسم المنتج' },
            { name: 'sku', label: 'الرمز التعريفي' },
            { name: 'sale_price', label: 'سعر البيع' },
            { name: 'category_id', label: 'الفئة' },
            { name: 'unit_id', label: 'الوحدة' },
            { name: 'sale_chartaccount_id', label: 'حساب الإيرادات' },
            { name: 'expense_chartaccount_id', label: 'حساب المصروفات' }
        ];

        requiredFields.forEach(function(field) {
            var value = $('[name="' + field.name + '"]').val();
            if (!value || value === '' || value === '0') {
                isValid = false;
                errorMessage += '• ' + field.label + ' مطلوب\n';
            }
        });

        // Check quantity for products
        var type = $('input[name="type"]:checked').val();
        if (type === 'product') {
            var quantity = $('input[name="quantity"]').val();
            if (!quantity || quantity === '') {
                isValid = false;
                errorMessage += '• الكمية مطلوبة للمنتجات\n';
            }
        }

        // Check prices are numeric and positive
        var salePrice = parseFloat($('input[name="sale_price"]').val());
        var purchasePrice = parseFloat($('input[name="purchase_price"]').val());

        if (isNaN(salePrice) || salePrice < 0) {
            isValid = false;
            errorMessage += '• سعر البيع يجب أن يكون رقم موجب\n';
        }

        if (purchasePrice && (isNaN(purchasePrice) || purchasePrice < 0)) {
            isValid = false;
            errorMessage += '• سعر الشراء يجب أن يكون رقم موجب\n';
        }

        if (!isValid) {
            e.preventDefault();
            alert('يرجى تصحيح الأخطاء التالية:\n\n' + errorMessage);
            return false;
        }

        // Show loading state
        var submitBtn = $(this).find('input[type="submit"]');
        var originalText = submitBtn.val();
        submitBtn.val('جاري الحفظ...').prop('disabled', true);

        // Allow form to submit normally
        return true;
    });
});
</script>
