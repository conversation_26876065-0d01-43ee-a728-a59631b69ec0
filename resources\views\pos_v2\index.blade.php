@php
    // استخدام المتغير المرسل من المتحكم
    $themeColor = 'theme-3';
    if (!empty($settings['color'])) {
        $themeColor = $settings['color'];
    }

    // إصلاح مسار الشعار
    $logo = \App\Models\Utility::get_file('uploads/logo');
    $company_favicon = isset($settings['company_favicon']) && !empty($settings['company_favicon']) ? $settings['company_favicon'] : 'favicon.png';
@endphp

<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ env('SITE_RTL') == 'on' ? 'rtl' : '' }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>
        {{ !empty($settings['header_text']) ? $settings['header_text'] : config('app.name', 'ERPGO SaaS') }}
        - {{ __('POS V2') }}</title>

    <link rel="icon"
        href="{{ $logo . '/' . $company_favicon }}"
        type="image" sizes="16x16">
    <link rel="stylesheet" href="{{ asset('assets/fonts/tabler-icons.min.css') }}">
    <link rel="stylesheet" href="{{ asset('css/site.css') }}" id="stylesheet">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    @if (env('SITE_RTL') == 'on')
        <link rel="stylesheet" href="{{ asset('css/bootstrap-rtl.css') }}">
    @endif

    <style>
        .pos-top-bar {
            padding: 10px 15px;
            border-radius: 5px;
        }

        .sop-card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .product-card {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 10px;
            margin: 5px;
            text-align: center;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-color: #007bff;
        }

        .product-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 6px;
            margin-bottom: 10px;
        }

        .default-product-image {
            width: 100%;
            height: 120px;
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            font-weight: bold;
            font-size: 24px;
            border-radius: 6px;
            margin-bottom: 10px;
        }

        .product-info {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .cart-product-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .category-btn {
            margin: 2px;
            border-radius: 20px;
        }

        .category-btn.active {
            background-color: #007bff;
            color: white;
        }

        .total-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
        }
    </style>
</head>

<body class="{{ $themeColor }}">
    <div class="container-fluid px-2">
        <?php $lastsegment = 'pos_v2'; ?>
        <div class="row">
            <div class="col-12">
                <div class="mt-2 pos-top-bar bg-color d-flex justify-content-between bg-primary">
                    <span class="text-white">{{ __('POS V2 - Advanced Point of Sale') }}</span>
                    <a href="{{ route('dashboard') }}" class="text-white"><i class="ti ti-home"
                            style="font-size: 20px;"></i> </a>
                </div>
            </div>
        </div>

        <div class="mt-2 row">
            <!-- Products Section -->
            <div class="col-lg-7">
                <div class="sop-card card">
                    <div class="card-header p-2">
                        <div class="search-bar-left">
                            <form>
                                <div class="row">
                                    <div class="col-md-4">
                                        {{ Form::select('customer', $customers, $cid, ['class' => 'form-control customer_select', 'id' => 'customer', 'required' => 'required']) }}
                                    </div>
                                    <div class="col-md-4">
                                        {{ Form::select('warehouse', $warehouses, null, ['class' => 'form-control warehouse_select', 'id' => 'warehouse', 'required' => 'required']) }}
                                    </div>
                                    <div class="col-md-4">
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text"><i class="ti ti-search"></i></span>
                                            </div>
                                            <input type="text" data-url="{{ route('search.products') }}"
                                                placeholder="{{ __('Search by Barcode Scanner') }}"
                                                class="form-control pr-4 rounded-right barcodeScanner">
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-12">
                                        <input type="text" data-url="{{ route('search.products') }}"
                                            placeholder="{{ __('Search Products by Name') }}"
                                            class="form-control searchproduct">
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="card-body p-2">
                        <div class="right-content">
                            <!-- Categories -->
                            <div class="button-list b-bottom catgory-pad">
                                <div class="form-row m-0" id="categories-listing">
                                    <!-- Categories will be loaded here -->
                                </div>
                            </div>
                            <!-- Products -->
                            <div class="product-body-nop">
                                <div class="form-row" id="product-listing">
                                    <!-- Products will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cart Section -->
            <div class="col-lg-5 ps-lg-0">
                <div class="card m-0">
                    <div class="card-header p-2">
                        <div class="row">
                            <div class="col-12">
                                <h5 class="mb-0">{{ __('Shopping Cart') }}</h5>
                                {{ Form::hidden('vc_name_hidden', '', ['id' => 'vc_name_hidden']) }}
                                {{ Form::hidden('warehouse_name_hidden', '', ['id' => 'warehouse_name_hidden']) }}
                                {{ Form::hidden('discount_hidden', '', ['id' => 'discount_hidden']) }}
                                {{ Form::hidden('delivery_user_hidden', '', ['id' => 'delivery_user_hidden']) }}
                            </div>
                        </div>
                    </div>
                    <div class="card-body carttable cart-product-list carttable-scroll" id="carthtml">
                        @php
                            $total = 0;
                            $netPrice = 0;
                            $discount = 0;
                        @endphp
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th></th>
                                        <th class="text-left">{{ __('Name') }}</th>
                                        <th class="text-center">{{ __('QTY') }}</th>
                                        <th>{{ __('Tax') }}</th>
                                        <th class="text-center">{{ __('Price') }}</th>
                                        <th class="text-center">{{ __('Sub Total') }}</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody id="tbody">
                                    @if (session($lastsegment) && !empty(session($lastsegment)) && count(session($lastsegment)) > 0)
                                        @foreach (session($lastsegment) as $id => $details)
                                            @php
                                                $product = \App\Models\ProductService::find($details['product_id']);
                                                $subtotal = $details['price'] * $details['quantity'];
                                                $netPrice += $subtotal;
                                                $discount += $details['discount'];
                                            @endphp
                                            <tr id="product-id-{{ $details['id'] }}">
                                                <td>
                                                    <button class="btn btn-sm btn-danger remove-from-cart" data-id="{{ $details['id'] }}">
                                                        <i class="ti ti-trash"></i>
                                                    </button>
                                                </td>
                                                <td class="text-left">{{ $product->name ?? $details['name'] }}</td>
                                                <td class="text-center">
                                                    <span class="quantity-area">
                                                        <input type="number" name="quantity" value="{{ $details['quantity'] }}"
                                                               min="1" class="form-control quantity-input text-center"
                                                               data-id="{{ $details['id'] }}" style="width: 70px;">
                                                    </span>
                                                </td>
                                                <td>{{ Auth::user()->priceFormat(0) }}</td>
                                                <td class="text-center">{{ Auth::user()->priceFormat($details['price']) }}</td>
                                                <td class="text-center">{{ Auth::user()->priceFormat($subtotal) }}</td>
                                                <td></td>
                                            </tr>
                                        @endforeach
                                    @else
                                        <tr class="no-found">
                                            <td colspan="7" class="text-center">{{ __('No items in cart') }}</td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Cart Totals -->
                    <div class="card-footer">
                        <div class="total-section">
                            <div class="row">
                                <div class="col-6">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <h6>{{ __('Sub Total') }} :</h6>
                                        <h6 class="subtotalamount">{{ Auth::user()->priceFormat($netPrice) }}</h6>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="d-flex align-items-center justify-content-end">
                                        <h6>{{ __('Discount') }} :</h6>
                                        <h6 class="discountamount">{{ Auth::user()->priceFormat($discount) }}</h6>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <h6>{{ __('Tax') }} :</h6>
                                        <h6 class="taxamount">{{ Auth::user()->priceFormat(0) }}</h6>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="d-flex align-items-center justify-content-end">
                                        <h6 class="fw-bold">{{ __('Total') }} :</h6>
                                        <h6 class="totalamount fw-bold">{{ Auth::user()->priceFormat($netPrice) }}</h6>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex align-items-center justify-content-between pt-3" id="btn-pur">
                                <button id="payment" type="button" class="btn btn-primary rounded" data-ajax-popup="true"
                                    data-size="xl" data-align="centered" data-url="{{ route('pos-v2.store') }}"
                                    data-title="{{ __('POS V2 Invoice') }}"
                                    @if (session($lastsegment) && !empty(session($lastsegment)) && count(session($lastsegment)) > 0) @else disabled="disabled" @endif>
                                    {{ __('PAY') }}
                                </button>
                                <div class="tab-content btn-empty text-end">
                                    <button class="btn btn-danger btn-clear-cart rounded">{{ __('Empty Cart') }}</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Common Modal -->
    <div class="modal fade" id="commonModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body body">
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="{{ asset('js/jquery.min.js') }}"></script>
    <script src="{{ asset('assets/js/plugins/bootstrap.min.js') }}"></script>
    <script src="{{ asset('assets/js/plugins/perfect-scrollbar.min.js') }}"></script>
    <script src="{{ asset('assets/js/plugins/feather.min.js') }}"></script>
    <script src="{{ asset('assets/js/plugins/apexcharts.min.js') }}"></script>
    <!-- تم حذف main.min.js لتجنب تحذيرات FullCalendar في صفحة POS -->
    <script src="{{ asset('assets/js/plugins/choices.min.js') }}"></script>
    <script src="{{ asset('assets/js/plugins/flatpickr.min.js') }}"></script>
    <script src="{{ asset('js/jscolor.js') }}"></script>
    <script src="{{ asset('js/custom.js') }}"></script>

    @if ($message = Session::get('success'))
        <script>
            show_toastr('success', '{!! $message !!}');
        </script>
    @endif
    @if ($message = Session::get('error'))
        <script>
            show_toastr('error', '{!! $message !!}');
        </script>
    @endif

    <script src="{{ asset('js/jquery-ui.min.js') }}"></script>

    <script>
        $(document).ready(function() {
            // Initialize hidden fields
            $('#vc_name_hidden').val($('.customer_select').val());
            $('#delivery_user_hidden').val($('.user_select').val());
            $('#warehouse_name_hidden').val($('.warehouse_select').val());
            $('#discount_hidden').val($('.discount').val());

            // Load categories and products
            getProductCategories();

            if ($('.searchproduct').length > 0) {
                var url = $('.searchproduct').data('url');
                var warehouse_id = $('#warehouse').val() || '0';
                searchProducts(url, '', '0', warehouse_id, 'name');
            }

            if ($('.barcodeScanner').length > 0) {
                var url = $('.barcodeScanner').data('url');
                var warehouse_id = $('#warehouse').val() || '0';
                searchProducts(url, '', '0', warehouse_id, 'sku');
            }

            // Warehouse change event
            $('#warehouse').change(function() {
                var ware_id = $("#warehouse").val();
                var url = $('.searchproduct').data('url');

                $('.searchproduct').val('');
                $('.barcodeScanner').val('');

                searchProducts(url, '', '0', ware_id);
                $('#warehouse_name_hidden').val(ware_id);
            });

            // Customer change event
            $('#customer').change(function() {
                $('#vc_name_hidden').val($(this).val());
            });

            // Product search by name
            $(document).on('keyup', 'input.searchproduct', function() {
                var selectedCustomer = $('#customer').val();
                if (!selectedCustomer) {
                    show_toastr('error', '{{ __("Please select a customer first!") }}', 'error');
                    $(this).val('');
                    return false;
                }

                var url = $(this).data('url');
                var value = this.value;
                if (value != '') {
                    var cat = $('.cat-active').children().data('cat-id');
                    var warehouse_id = $('#warehouse').val();
                    searchProducts(url, value, cat, warehouse_id, 'name');
                }
            });

            // Barcode scanner
            $(document).on('keyup', 'input.barcodeScanner', function() {
                var selectedCustomer = $('#customer').val();
                if (!selectedCustomer) {
                    show_toastr('error', '{{ __("Please select a customer first!") }}', 'error');
                    $(this).val('');
                    return false;
                }

                var url = $(this).data('url');
                var value = this.value;
                if (value != '') {
                    var warehouse_id = $('#warehouse').val();
                    searchProducts(url, value, '0', warehouse_id, 'sku');
                }
            });

            // Category click
            $(document).on('click', '.category-btn', function() {
                $('.category-btn').removeClass('active');
                $(this).addClass('active');

                var cat_id = $(this).data('cat-id');
                var url = $('.searchproduct').data('url');
                var warehouse_id = $('#warehouse').val();
                searchProducts(url, '', cat_id, warehouse_id, 'name');
            });

            // Add to cart - استخدام فئة toacart المستخدمة في عرض المنتجات
            $(document).on('click', '.toacart', function() {
                var selectedCustomer = $('#customer').val();
                if (!selectedCustomer) {
                    show_toastr('error', '{{ __("Please select a customer first!") }}', 'error');
                    return false;
                }

                var selectedWarehouse = $('#warehouse').val();
                if (!selectedWarehouse) {
                    show_toastr('error', '{{ __("Please select a warehouse first!") }}', 'error');
                    return false;
                }

                // استخراج معرف المنتج من URL
                var url = $(this).data('url');
                if (!url) {
                    show_toastr('error', '{{ __("Product URL not found!") }}', 'error');
                    return false;
                }

                // استخراج معرف المنتج من URL مثل: add-to-cart/123/pos_v2
                var urlParts = url.split('/');
                var product_id = urlParts[urlParts.length - 2]; // المعرف قبل الأخير
                var session_key = '{{ $lastsegment }}';

                $.ajax({
                    url: '{{ route("pos_v2.add_to_cart") }}',
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        product_id: product_id,
                        session_key: session_key
                    },
                    success: function(data) {
                        if (data.error) {
                            show_toastr('error', data.error, 'error');
                        } else {
                            // Update cart
                            if ($('#product-id-' + data.product.id).length > 0) {
                                $('#product-id-' + data.product.id + ' input[name="quantity"]').val(data.product.quantity);
                            } else {
                                $('#tbody').append(data.carthtml);
                                $('.no-found').addClass('d-none');
                            }

                            $('#btn-pur button').removeAttr('disabled');
                            $('.btn-empty button').addClass('btn-clear-cart');
                            updateCartTotals();
                        }
                    },
                    error: function(data) {
                        data = data.responseJSON;
                        show_toastr('error', data.error, 'error');
                    }
                });
            });

            // Remove from cart
            $(document).on('click', '.remove-from-cart', function() {
                var product_id = $(this).data('id');
                var session_key = '{{ $lastsegment }}';

                $.ajax({
                    url: '{{ route("pos_v2.remove_from_cart") }}',
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        product_id: product_id,
                        session_key: session_key
                    },
                    success: function(data) {
                        $('#product-id-' + product_id).remove();
                        updateCartTotals();

                        if ($('#tbody tr').length <= 1) {
                            $('#tbody').html('<tr class="no-found"><td colspan="7" class="text-center">{{ __("No items in cart") }}</td></tr>');
                            $('#btn-pur button').attr('disabled', 'disabled');
                        }
                    }
                });
            });

            // Update quantity
            $(document).on('change keyup', '#carthtml input[name="quantity"]', function() {
                var product_id = $(this).data('id');
                var quantity = $(this).val();
                var session_key = '{{ $lastsegment }}';

                $.ajax({
                    url: '{{ route("pos_v2.update_cart") }}',
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        product_id: product_id,
                        quantity: quantity,
                        session_key: session_key
                    },
                    success: function(data) {
                        updateCartTotals();
                    }
                });
            });

            // Clear cart
            $(document).on('click', '.btn-clear-cart', function() {
                var session_key = '{{ $lastsegment }}';

                $.ajax({
                    url: '{{ route("pos_v2.empty_cart") }}',
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        session_key: session_key
                    },
                    success: function(data) {
                        $('#tbody').html('<tr class="no-found"><td colspan="7" class="text-center">{{ __("No items in cart") }}</td></tr>');
                        $('#btn-pur button').attr('disabled', 'disabled');
                        updateCartTotals();
                    }
                });
            });

            // Payment button - استخدام data-ajax-popup للتوافق مع النظام العام
            $(document).on('click', '#payment', function(e) {
                e.preventDefault();

                // التحقق من وجود منتجات في السلة
                if (!$(this).data('url') || $(this).is(':disabled')) {
                    show_toastr('error', '{{ __("Please add products to cart first!") }}', 'error');
                    return false;
                }

                // التحقق من اختيار العميل
                var selectedCustomer = $('#customer').val();
                if (!selectedCustomer) {
                    show_toastr('error', '{{ __("Please select a customer first!") }}', 'error');
                    return false;
                }

                // التحقق من اختيار المستودع
                var selectedWarehouse = $('#warehouse').val();
                if (!selectedWarehouse) {
                    show_toastr('error', '{{ __("Please select a warehouse first!") }}', 'error');
                    return false;
                }

                // استخدام النظام العام للمودال
                var url = $(this).data('url');
                var title = $(this).data('title');
                var size = $(this).data('size') || 'xl';

                var data = {
                    vc_name: selectedCustomer,
                    warehouse_name: selectedWarehouse,
                    discount: 0 // يمكن إضافة حقل خصم لاحقاً
                };

                $('#commonModal .modal-dialog').removeClass('modal-sm modal-md modal-lg modal-xl modal-xxl');
                $('#commonModal .modal-dialog').addClass('modal-' + size);
                $('#commonModal .modal-title').text(title);

                $.ajax({
                    url: url,
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        ...data
                    },
                    beforeSend: function() {
                        $('#payment').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status"></span> {{ __("Processing...") }}');
                    },
                    success: function(response) {
                        $('#commonModal .modal-body').html(response);
                        $('#commonModal').modal('show');
                        $('#payment').prop('disabled', false).html('{{ __("PAY") }}');
                    },
                    error: function(xhr) {
                        var data = xhr.responseJSON;
                        show_toastr('error', data.error || '{{ __("An error occurred") }}', 'error');
                        $('#payment').prop('disabled', false).html('{{ __("PAY") }}');
                    }
                });
            });

            // Search products function
            function searchProducts(url, value, cat_id, war_id = '0', type) {
                var selectedCustomer = $('#customer').val();
                if (!selectedCustomer) {
                    show_toastr('error', '{{ __("Please select a customer first!") }}', 'error');
                    return false;
                }

                var session_key = '{{ $lastsegment }}';
                $.ajax({
                    type: 'GET',
                    url: url,
                    data: {
                        'search': value,
                        'cat_id': cat_id,
                        'war_id': war_id,
                        'session_key': session_key,
                        'type': type,
                    },
                    success: function(data) {
                        $('#product-listing').html(data);
                    }
                });
            }

            // Get product categories
            function getProductCategories() {
                $.ajax({
                    type: 'GET',
                    url: '{{ route('product.categories') }}',
                    success: function (data) {
                        $('#categories-listing').html(data);
                    }
                });
            }

            // Update cart totals
            function updateCartTotals() {
                var subtotal = 0;
                var tax = 0;
                var discount = 0;

                $('#tbody tr:not(.no-found)').each(function() {
                    var quantity = $(this).find('input[name="quantity"]').val() || 0;
                    var price = parseFloat($(this).find('td:nth-child(5)').text().replace(/[^\d.-]/g, '')) || 0;
                    subtotal += (price * quantity);
                });

                $('.subtotalamount').text(addCommas(subtotal.toFixed(2)));
                $('.taxamount').text(addCommas(tax.toFixed(2)));
                $('.discountamount').text(addCommas(discount.toFixed(2)));
                $('.totalamount').text(addCommas((subtotal + tax - discount).toFixed(2)));
            }

            // Add commas to numbers
            function addCommas(num) {
                return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
            }
        });
    </script>
