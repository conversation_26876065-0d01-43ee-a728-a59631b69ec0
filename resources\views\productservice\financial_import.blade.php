{{ Form::open(['route' => 'financial.products.import.process', 'method' => 'post', 'enctype' => 'multipart/form-data', 'id' => 'financial-import-form']) }}
<div class="modal-body">
    <div class="row">
        <!-- تحميل نموذج CSV -->
        <div class="col-md-12 mb-3">
            <div class="alert alert-info">
                <h6><i class="ti ti-info-circle"></i> {{ __('تعليمات الاستيراد') }}</h6>
                <ul class="mb-0">
                    <li>{{ __('قم بتحميل نموذج CSV أولاً') }}</li>
                    <li>{{ __('املأ البيانات في النموذج') }}</li>
                    <li>{{ __('ارفع الملف المكتمل') }}</li>
                </ul>
            </div>
        </div>

        <!-- الإعدادات الافتراضية -->
        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('default_income_account', __('حساب الإيرادات الافتراضي'), ['class' => 'form-label']) }}
                {{ Form::select('default_income_account', $incomeChartAccounts, null, ['class' => 'form-control select', 'required' => 'required']) }}
            </div>
        </div>

        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('default_expense_account', __('حساب المصروفات الافتراضي'), ['class' => 'form-label']) }}
                {{ Form::select('default_expense_account', $expenseChartAccounts, null, ['class' => 'form-control select', 'required' => 'required']) }}
            </div>
        </div>

        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('default_category', __('الفئة الافتراضية'), ['class' => 'form-label']) }}
                {{ Form::select('default_category', $category, null, ['class' => 'form-control select', 'required' => 'required']) }}
            </div>
        </div>

        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('default_unit', __('الوحدة الافتراضية'), ['class' => 'form-label']) }}
                {{ Form::select('default_unit', $units, null, ['class' => 'form-control select', 'required' => 'required']) }}
            </div>
        </div>

        <!-- تحميل نموذج CSV -->
        <div class="col-md-12 mb-3">
            <div class="d-flex justify-content-between align-items-center">
                {{ Form::label('download_template', __('تحميل نموذج CSV'), ['class' => 'form-label']) }}
                <button type="button" id="download-template" class="btn btn-sm btn-info">
                    <i class="ti ti-download"></i> {{ __('تحميل النموذج') }}
                </button>
            </div>
        </div>

        <!-- رفع ملف CSV -->
        <div class="col-md-12">
            {{ Form::label('import_file', __('اختر ملف CSV'), ['class' => 'form-label']) }}
            <div class="form-group">
                {{ Form::file('import_file', ['class' => 'form-control', 'accept' => '.csv,.txt', 'required' => 'required']) }}
                <small class="text-muted">{{ __('الصيغ المدعومة: CSV, TXT') }}</small>
            </div>
        </div>

        <!-- خيارات إضافية -->
        <div class="col-md-12 mt-3">
            <div class="form-check">
                {{ Form::checkbox('delete_existing', 1, false, ['class' => 'form-check-input', 'id' => 'delete_existing']) }}
                {{ Form::label('delete_existing', __('حذف جميع المنتجات الموجودة قبل الاستيراد'), ['class' => 'form-check-label']) }}
                <small class="text-danger d-block">{{ __('تحذير: سيتم حذف جميع المنتجات الحالية!') }}</small>
            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <input type="button" value="{{ __('إلغاء') }}" class="btn btn-secondary" data-bs-dismiss="modal">
    <input type="submit" value="{{ __('استيراد') }}" class="btn btn-primary">
</div>
{{ Form::close() }}

<script>
$(document).ready(function() {
    // Download template
    $('#download-template').on('click', function() {
        var defaultIncomeAccount = $('select[name="default_income_account"]').val();
        var defaultExpenseAccount = $('select[name="default_expense_account"]').val();
        var defaultCategory = $('select[name="default_category"]').val();
        var defaultUnit = $('select[name="default_unit"]').val();

        if (!defaultIncomeAccount || !defaultExpenseAccount || !defaultCategory || !defaultUnit) {
            alert('يرجى اختيار جميع الإعدادات الافتراضية أولاً');
            return;
        }

        var url = '{{ route("financial.products.download.template") }}';
        var form = $('<form method="POST" action="' + url + '">');
        form.append('<input type="hidden" name="_token" value="{{ csrf_token() }}">');
        form.append('<input type="hidden" name="default_income_account" value="' + defaultIncomeAccount + '">');
        form.append('<input type="hidden" name="default_expense_account" value="' + defaultExpenseAccount + '">');
        form.append('<input type="hidden" name="default_category" value="' + defaultCategory + '">');
        form.append('<input type="hidden" name="default_unit" value="' + defaultUnit + '">');
        $('body').append(form);
        form.submit();
        form.remove();
    });

    // Form submission
    $('#financial-import-form').on('submit', function(e) {
        var deleteExisting = $('#delete_existing').is(':checked');
        
        if (deleteExisting) {
            if (!confirm('هل أنت متأكد من حذف جميع المنتجات الموجودة؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                e.preventDefault();
                return false;
            }
        }

        // Show loading state
        var submitBtn = $(this).find('input[type="submit"]');
        var originalText = submitBtn.val();
        submitBtn.val('جاري الاستيراد...').prop('disabled', true);

        // Allow form to submit
        return true;
    });

    // File validation
    $('input[name="import_file"]').on('change', function() {
        var file = this.files[0];
        if (file) {
            var fileName = file.name;
            var fileExtension = fileName.split('.').pop().toLowerCase();
            
            if (fileExtension !== 'csv' && fileExtension !== 'txt') {
                alert('يرجى اختيار ملف CSV أو TXT فقط');
                $(this).val('');
                return false;
            }
            
            // Check file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('حجم الملف كبير جداً. الحد الأقصى 5MB');
                $(this).val('');
                return false;
            }
        }
    });
});
</script>
