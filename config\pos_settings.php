<?php

return [
    /*
    |--------------------------------------------------------------------------
    | POS Display Settings
    |--------------------------------------------------------------------------
    |
    | These settings control how products are displayed in the POS system
    |
    */

    // إعدادات عرض الصور في POS
    'show_product_images' => env('POS_SHOW_PRODUCT_IMAGES', true),
    
    // إعدادات عرض الصور في السلة
    'show_cart_images' => env('POS_SHOW_CART_IMAGES', true),
    
    // إعدادات عرض الصور في قائمة المنتجات
    'show_product_list_images' => env('POS_SHOW_PRODUCT_LIST_IMAGES', true),
    
    // الصورة الافتراضية عند عدم وجود صورة
    'default_product_image' => env('POS_DEFAULT_PRODUCT_IMAGE', 'storage/uploads/pro_image/default.png'),
    
    // إعدادات أخرى للـ POS
    'items_per_page' => env('POS_ITEMS_PER_PAGE', 20),
    'enable_barcode_scanner' => env('POS_ENABLE_BARCODE_SCANNER', true),
    'auto_print_receipt' => env('POS_AUTO_PRINT_RECEIPT', false),
    
    // إعدادات الدفع
    'payment_methods' => [
        'cash' => [
            'enabled' => env('POS_CASH_ENABLED', true),
            'name' => 'نقدي',
            'icon' => 'ti-cash'
        ],
        'network' => [
            'enabled' => env('POS_NETWORK_ENABLED', true),
            'name' => 'شبكة',
            'icon' => 'ti-credit-card'
        ],
        'split' => [
            'enabled' => env('POS_SPLIT_ENABLED', true),
            'name' => 'مختلط',
            'icon' => 'ti-wallet'
        ]
    ],
    
    // إعدادات الطباعة الحرارية
    'thermal_print' => [
        'enabled' => env('POS_THERMAL_PRINT_ENABLED', true),
        'paper_width' => env('POS_THERMAL_PAPER_WIDTH', 80), // mm
        'auto_cut' => env('POS_THERMAL_AUTO_CUT', true),
        'logo_enabled' => env('POS_THERMAL_LOGO_ENABLED', true),
    ]
];
