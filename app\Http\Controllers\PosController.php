<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use App\Models\Pos;
use App\Models\PosPayment;
use App\Models\PosProduct;
use App\Models\PosReturn;
use App\Models\PosReturnItem;
use App\Models\ProductService;
use App\Models\Quotation;
use App\Models\QuotationProduct;
use App\Models\Shift;
use App\Models\StockReport;
use App\Models\User;
use App\Models\Utility;
use App\Models\warehouse;
use App\Models\WarehouseProduct;
use App\Models\FinancialRecord;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;
use Mpdf\Mpdf;
use DNS1D;

class PosController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index($id = 0)
    {

        if (Auth::user()->can('manage pos')) {
            session()->forget('pos');

            // تحديد المستودع المحدد (إذا كان موجوداً)
            $selectedWarehouseId = request()->get('warehouse_id', \Auth::user()->warehouse_id);

            // الحصول على قائمة المستودعات المتاحة
            if(\Auth::user()->warehouse_id == NULL) {
                $warehouses = warehouse::select('*', \DB::raw("CONCAT(name) AS name"))->where('created_by', \Auth::user()->creatorId())->get()->pluck('name', 'id');
            } else {
                $warehouses = warehouse::select('*', \DB::raw("CONCAT(name) AS name"))->where('created_by', \Auth::user()->creatorId())->where('id', \Auth::user()->warehouse_id)->get()->pluck('name', 'id');
            }

            // تصفية العملاء حسب المستودع المحدد
            $customersQuery = Customer::where('created_by', \Auth::user()->creatorId());

            // التحقق من وجود عمود warehouse_id في جدول العملاء قبل استخدامه في الاستعلام
            if (Schema::hasColumn('customers', 'warehouse_id')) {
                if (!empty($selectedWarehouseId)) {
                    // إظهار العملاء المرتبطين بالمستودع المحدد أو العملاء غير المرتبطين بأي مستودع (warehouse_id = null أو '')
                    $customersQuery->where(function($query) use ($selectedWarehouseId) {
                        $query->where('warehouse_id', $selectedWarehouseId)
                              ->orWhereNull('warehouse_id')
                              ->orWhere('warehouse_id', '');
                    });
                }
                // إذا لم يتم تحديد مستودع، نعرض جميع العملاء
            }

            $customers = $customersQuery->get()->pluck('name', 'id');
            $user = Auth::user();
            $deliveryUsers = User::where('warehouse_id', \Auth::user()->warehouse_id)
                ->where('type', 'delivery')->get()->pluck('name', 'id');
            $details = [
                'pos_id' => $user->posNumberFormat($this->invoicePosNumber()),
                'customer' => $customers != null ? $customers->toArray() : [],
                'user' => $user != null ? $user->toArray() : [],
                'date' => date('Y-m-d'),
                'pay' => 'show',
            ];

            if ($id != 0) {
                $quotation = Quotation::find($id);
                $customerId = $quotation->customer_id;
                $customerId = Customer::find($customerId);
                $customer = $customerId->name;
                $warehouseId = $quotation->warehouse_id;
                $quotationProduct = QuotationProduct::where('quotation_id', $id)->get();
                foreach ($quotationProduct as $value) {
                    $products = Quotation::quotationProduct($value);
                }
            } else {
                $customer = '';
                $warehouseId = '';
            }

            // إذا كان الطلب من خلال AJAX، أعد البيانات بتنسيق JSON
            if (request()->ajax() && request()->get('ajax') == 1) {
                return response()->json([
                    'customers' => $customers,
                    'warehouses' => $warehouses,
                    'selectedWarehouseId' => $selectedWarehouseId
                ]);
            }

            return view('pos.index', compact('customers', 'warehouses', 'details', 'customer', 'warehouseId', 'id','deliveryUsers'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        // dd($request->all());
        $sess = session()->get('pos');

        if (Auth::user()->can('manage pos') && isset($sess) && !empty($sess) && count($sess) > 0) {




            $user = Auth::user();

            $settings = Utility::settings();

            $customer = Customer::where('id', '=', $request->vc_name)->where('created_by', $user->creatorId())->first();
            $warehouse = warehouse::where('id', '=', $request->warehouse_name)->where('created_by', $user->creatorId())->first();


            $details = [
                'pos_id' => $user->posNumberFormat($this->invoicePosNumber()),
                'customer' => $customer != null ? $customer->toArray() : [],
                'warehouse' => $warehouse != null ? $warehouse->toArray() : [],
                'user' => $user != null ? $user->toArray() : [],
                'date' => date('Y-m-d'),
                'pay' => 'show',
            ];

            if (!empty($details['customer'])) {
                $warehousedetails = '<h7 class="text-dark">' . ucfirst($details['warehouse']['name']) . '</p></h7>';
                $details['customer']['billing_state'] = $details['customer']['billing_state'] != '' ? ", " . $details['customer']['billing_state'] : '';
                $details['customer']['shipping_state'] = $details['customer']['shipping_state'] != '' ? ", " . $details['customer']['shipping_state'] : '';

                $customerdetails = '<h6 class="text-dark">' . ucfirst($details['customer']['name']) . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['billing_phone'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['billing_address'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['billing_city'] . $details['customer']['billing_state'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['billing_country'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['billing_zip'] . '</p></h6>';

                $shippdetails = '<h6 class="text-dark"><b>' . ucfirst($details['customer']['name']) . '</b>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['shipping_phone'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['shipping_address'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['shipping_city'] . $details['customer']['shipping_state'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['shipping_country'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['shipping_zip'] . '</p></h6>';

            } else {
                $customerdetails = '<h2 class="h6"><b>' . __('Walk-in Customer') . '</b><h2>';
                $warehousedetails = '<h7 class="text-dark">' . ucfirst($details['warehouse']['name']) . '</p></h7>';
                $shippdetails = '-';

            }

            $settings['company_telephone'] = $settings['company_telephone'] != '' ? ", " . $settings['company_telephone'] : '';
            $settings['company_state'] = $settings['company_state'] != '' ? ", " . $settings['company_state'] : '';

            $userdetails = '<h6 class="text-dark"><b>' . ucfirst($details['user']['name']) . ' </b> <h2  class="font-weight-normal">' . '<p class="m-0 font-weight-normal">' . $settings['company_name'] . $settings['company_telephone'] . '</p>' . '<p class="m-0 font-weight-normal">' . $settings['company_address'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $settings['company_city'] . $settings['company_state'] . '</p>' . '<p class="m-0 font-weight-normal">' . $settings['company_country'] . '</p>' . '<p class="m-0 font-weight-normal">' . $settings['company_zipcode'] . '</p></h2>';

            $details['customer']['details'] = $customerdetails;
            $details['warehouse']['details'] = $warehousedetails;
            //
            $details['customer']['shippdetails'] = $shippdetails;

            $details['user']['details'] = $userdetails;

            $mainsubtotal = 0;
            $totalTax = 0;
            $sales = [];

            foreach ($sess as $key => $value) {
                // التحقق من وجود البيانات المطلوبة
                if (!isset($value['name']) || !isset($value['price']) || !isset($value['quantity'])) {
                    continue; // تخطي هذا العنصر إذا كانت البيانات ناقصة
                }

                // حساب المجموع الفرعي للمنتج (السعر × الكمية)
                $subtotal = $value['price'] * $value['quantity'];

                // حساب الضريبة للمنتج
                $tax_rate = isset($value['tax']) ? $value['tax'] : 0;
                $tax = ($subtotal * $tax_rate) / 100;
                $totalTax += $tax;

                $sales['data'][$key]['name'] = $value['name'];
                $sales['data'][$key]['quantity'] = $value['quantity'];
                $sales['data'][$key]['price'] = Auth::user()->priceFormat($value['price']);
                $sales['data'][$key]['tax'] = $tax_rate . '%';
                $sales['data'][$key]['tax_rate'] = $tax_rate . '%'; // إضافة معدل الضريبة
                $sales['data'][$key]['product_tax'] = isset($value['product_tax']) ? $value['product_tax'] : '';
                $sales['data'][$key]['tax_amount'] = Auth::user()->priceFormat($tax);
                $sales['data'][$key]['subtotal'] = Auth::user()->priceFormat(isset($value['subtotal']) ? $value['subtotal'] : $subtotal);
                $mainsubtotal += $subtotal; // استخدام subtotal بدلاً من value['subtotal'] للحصول على المجموع الفرعي قبل الضريبة
            }

            $discount = !empty($request->discount) ? $request->discount : 0;
            $sales['discount'] = Auth::user()->priceFormat($discount);

            // حساب المجموع الفرعي (قبل الضريبة)
            $sales['subtotal'] = $mainsubtotal;
            $sales['sub_total'] = Auth::user()->priceFormat($mainsubtotal);

            // حساب إجمالي الضريبة
            $sales['tax'] = $totalTax;
            $sales['tax_format'] = Auth::user()->priceFormat($totalTax);

            // حساب الإجمالي النهائي (المجموع الفرعي + الضريبة - الخصم)
            $total = $mainsubtotal + $totalTax - $discount;
            // تطبيق تنسيق الأرقام العشرية على القيمة الإجمالية لضمان التطابق مع العرض
            $sales['total'] = round($total, 2);
            return view('pos.show', compact('sales', 'details'));
        } else {
            return response()->json(
                [
                    'error' => __('Add some products to cart!'),
                ],
                '404'
            );
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $discount = $request->discount;

            //the current open shift for the user
            $warehouse = Auth::user()->warehouse_id;

            $openShift = Shift::whereNull('closed_at')->where('warehouse_id', $warehouse)->first();

            // التحقق من وجود علامة في الجلسة تشير إلى أن الدفع تم معالجته بالفعل
            $payment_processed = session()->get('payment_processed', false);

            if (Auth::user()->can('manage pos')) {

                // التحقق من وجود بيانات في الجلسة
                $sess = session()->get('pos');
                if (!$sess || empty($sess) || count($sess) == 0) {
                    return response()->json([
                        'code' => 404,
                        'error' => __('No items in cart')
                    ]);
                }

            if($request->quotation_id != 0)
            {
                $quotation = Quotation::where('id', $request->quotation_id)->first();
                $quotation->is_converted = 1;
                $quotation->save();
            }


            $user_id = Auth::user()->creatorId();
            $customer_id = Customer::where('id', '=', $request->vc_name)->first();
            $warehouse_id = warehouse::warehouse_id($request->warehouse_name);
            $pos_id = $this->invoicePosNumber();
            $sales = session()->get('pos');

            if (isset($sales) && !empty($sales) && count($sales) > 0) {
                $result = DB::table('pos')->where('pos_id', $pos_id)->where('created_by', $user_id)->get();
                if (count($result) > 0) {
                    return response()->json(
                        [
                            'code' => 200,
                            'success' => __('Payment is already completed!'),
                        ]
                    );
                } else {
                    $pos = new Pos();
                    $pos->pos_id = $pos_id;
                    $pos->customer_id = $customer_id->id;
                    $pos->warehouse_id = $request->warehouse_name;
                    $pos->user_id = $request->user_id;
                    $pos->pos_date = date('Y-m-d');
                    $pos->created_by = $user_id;
                    $pos->shift_id = $openShift->id;
                    $pos->save();

            if($request->quotation_id != 0)
        {
                    $quotation->converted_pos_id = $pos->id;
                    $quotation->save();
                }
                    foreach ($sales as $key => $value) {
                        $product_id = $value['id'];

                        $product = ProductService::whereId($product_id)->where('created_by', $user_id)->first();

                        // لا نحدث quantity في product_services لأننا نعتمد على warehouse_products

                        $tax_id = ProductService::tax_id($product_id);

                        $positems = new PosProduct();
                        $positems->pos_id = $pos->id;
                        $positems->product_id = $product_id;
                        $positems->price = $value['price'];
                        $positems->quantity = $value['quantity'];
                        $positems->tax = $tax_id;
                        $positems->discount = $discount;
                        $positems->save();

                        Utility::warehouse_quantity('minus', $positems->quantity, $positems->product_id, $request->warehouse_name);

                        //Product Stock Report
                        $type = 'pos';
                        $type_id = $pos->id;
                        StockReport::where('type', '=', 'pos')->where('type_id', '=', $pos->id)->delete();
                        $description = $positems->quantity . '  ' . __(' quantity sold in pos') . ' ' . \Auth::user()->posNumberFormat($pos->pos_id);
                        Utility::addProductStock($positems->product_id, $positems->quantity, $type, $description, $type_id);

                        // Registrar transacción de costo de ventas
                        $product = ProductService::find($product_id);
                        if ($product && $product->expense_chartaccount_id > 0) {
                            // Calcular el costo total del producto vendido usando el precio de compra
                            $costAmount = $product->purchase_price * $positems->quantity;

                            // Registrar transacción de costo de ventas (COGS)
                            $data = [
                                'account_id' => $product->expense_chartaccount_id,
                                'transaction_type' => 'Debit',
                                'transaction_amount' => $costAmount,
                                'reference' => 'EXP',
                                'reference_id' => $pos->id,
                                'reference_sub_id' => $product->id,
                                'date' => date('Y-m-d'),
                            ];
                            Utility::addTransactionLines($data, 'create');

                            // Registrar transacción de ventas
                            if ($product->sale_chartaccount_id > 0) {
                                $saleAmount = $positems->price * $positems->quantity;
                                $data = [
                                    'account_id' => $product->sale_chartaccount_id,
                                    'transaction_type' => 'Credit',
                                    'transaction_amount' => $saleAmount,
                                    'reference' => 'POS',
                                    'reference_id' => $pos->id,
                                    'reference_sub_id' => $product->id,
                                    'date' => date('Y-m-d'),
                                ];
                                Utility::addTransactionLines($data, 'create');
                            }
                        }
                    }

                    $posPayment = new PosPayment();
                    $posPayment->pos_id = $pos->id;
                    $posPayment->date = $request->date;
                    $posPayment->created_by = $user_id;

                    $mainsubtotal = 0;
                    $totalTax = 0;
                    $sales = [];

                    $sess = session()->get('pos');

                    foreach ($sess as $key => $value) {
                        // التحقق من وجود البيانات المطلوبة
                        if (!isset($value['name']) || !isset($value['price']) || !isset($value['quantity'])) {
                            continue; // تخطي هذا العنصر إذا كانت البيانات ناقصة
                        }

                        // حساب المجموع الفرعي للمنتج (السعر × الكمية)
                        $subtotal = $value['price'] * $value['quantity'];

                        // حساب الضريبة للمنتج
                        $tax_rate = isset($value['tax']) ? $value['tax'] : 0;
                        $tax = ($subtotal * $tax_rate) / 100;
                        $totalTax += $tax;

                        $sales['data'][$key]['name'] = $value['name'];
                        $sales['data'][$key]['price'] = Auth::user()->priceFormat($value['price']);
                        $sales['data'][$key]['tax'] = $tax_rate . '%';
                        $sales['data'][$key]['tax_rate'] = $tax_rate . '%'; // إضافة معدل الضريبة
                        $sales['data'][$key]['tax_amount'] = Auth::user()->priceFormat($tax);
                        $sales['data'][$key]['subtotal'] = Auth::user()->priceFormat($value['subtotal']);
                        $mainsubtotal += $subtotal; // استخدام subtotal بدلاً من value['subtotal'] للحصول على المجموع الفرعي قبل الضريبة
                    }

                    // حساب المجموع الفرعي (قبل الضريبة)
                    $sales['subtotal'] = $mainsubtotal;

                    // حساب إجمالي الضريبة
                    $sales['tax'] = $totalTax;
                    // حساب الإجمالي النهائي (المجموع الفرعي + الضريبة - الخصم)
                    $amount = $mainsubtotal + $totalTax;
                    $posPayment->amount = $amount;
                    $total = round($mainsubtotal + $totalTax - $discount, 2);
                    $posPayment->discount = $discount;
                    $posPayment->discount_amount = $total;

                    // إذا كان الدفع قد تمت معالجته بالفعل في FinancialRecordController@financialType
                    // نستخدم بيانات الدفع المخزنة في الجلسة
                    if ($payment_processed) {
                        $payment_data = session()->get('payment_data', []);

                        if (isset($payment_data['payment_type'])) {
                            $posPayment->payment_type = $payment_data['payment_type'];

                            // For split payments, use the values from session
                            if ($payment_data['payment_type'] == 'split') {
                                $cash_amount = isset($payment_data['split_cash_amount']) ? floatval($payment_data['split_cash_amount']) : 0;
                                $network_amount = isset($payment_data['split_network_amount']) ? floatval($payment_data['split_network_amount']) : 0;

                                $posPayment->cash_amount = $cash_amount;
                                $posPayment->network_amount = $network_amount;
                                $posPayment->transaction_number = isset($payment_data['split_transaction_number']) ? $payment_data['split_transaction_number'] : '';
                            }
                            // For cash payments
                            elseif ($payment_data['payment_type'] == 'cash') {
                                $posPayment->cash_amount = $total;
                                $posPayment->network_amount = 0;
                            }
                            // For network payments
                            elseif ($payment_data['payment_type'] == 'network') {
                                $posPayment->cash_amount = 0;
                                $posPayment->network_amount = $total;
                                $posPayment->transaction_number = isset($payment_data['transaction_number']) ? $payment_data['transaction_number'] : '';
                            }
                        }
                    }
                    // إذا لم تتم معالجة الدفع بعد، نستخدم البيانات من الطلب
                    else if (isset($request->payment_type)) {
                        $posPayment->payment_type = $request->payment_type;

                        // For split payments, save both cash and network amounts
                        if ($request->payment_type == 'split') {
                            // تأكد من أن القيم موجودة وصحيحة
                            $cash_amount = isset($request->split_cash_amount) ? floatval($request->split_cash_amount) : 0;
                            $network_amount = isset($request->split_network_amount) ? floatval($request->split_network_amount) : 0;

                            // التحقق من أن مجموع المبالغ يساوي السعر الإجمالي
                            $total_split = $cash_amount + $network_amount;
                            if (abs($total_split - $total) > 0.01) {
                                // إذا كان هناك فرق، نقوم بتعديل القيم لتتناسب مع المجموع
                                if ($total_split > 0) {
                                    $ratio = $total / $total_split;
                                    $cash_amount = round($cash_amount * $ratio, 2);
                                    $network_amount = $total - $cash_amount; // لضمان أن المجموع يساوي تماماً السعر الإجمالي
                                } else {
                                    $cash_amount = 0;
                                    $network_amount = $total;
                                }
                            }

                            $posPayment->cash_amount = $cash_amount;
                            $posPayment->network_amount = $network_amount;
                            $posPayment->transaction_number = isset($request->split_transaction_number) ? $request->split_transaction_number : '';
                        }
                        // For cash payments, set cash_amount to the total
                        elseif ($request->payment_type == 'cash') {
                            $posPayment->cash_amount = $total;
                            $posPayment->network_amount = 0;
                        }
                        // For network payments, set network_amount to the total
                        elseif ($request->payment_type == 'network') {
                            $posPayment->cash_amount = 0;
                            $posPayment->network_amount = $total;
                            $posPayment->transaction_number = isset($request->transaction_number) ? $request->transaction_number : '';
                        }
                    }
                    // إذا لم يتم تحديد نوع الدفع، نستخدم النقد كافتراضي
                    else {
                        $posPayment->payment_type = 'cash';
                        $posPayment->cash_amount = $total;
                        $posPayment->network_amount = 0;
                    }

                    $posPayment->save();

                    // إزالة بيانات الجلسة
                    session()->forget('pos');
                    session()->forget('payment_processed');
                    session()->forget('payment_data');

                    return response()->json(
                        [
                            'code' => 200,
                            'success' => __('Payment completed successfully!'),
                            'pos_id' => $pos->id,
                            'pos_number' => \Auth::user()->posNumberFormat($pos->pos_id),
                        ]
                    );
                }
            } else {
                return response()->json(
                    [
                        'code' => 404,
                        'success' => __('Items not found!'),
                    ]
                );
            }
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
        } catch (\Exception $e) {
            \Log::error('POS Store Error: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'request_data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'code' => 500,
                'error' => __('An error occurred while processing payment. Please try again.')
            ]);
        }
    }

    public function show($ids)
    {

        if (\Auth::user()->can('show pos') || \Auth::user()->type == 'company') {
            try {
                $id = Crypt::decrypt($ids);
            } catch (\Throwable $th) {
                return redirect()->back()->with('error', __('Pos Not Found.'));
            }

            $id = Crypt::decrypt($ids);

            $pos = Pos::find($id);

            if ($pos->created_by == \Auth::user()->creatorId()) {
                $posPayment = PosPayment::where('pos_id', $pos->id)->first();
                $customer = $pos->customer;
                $iteams = $pos->items;

                return view('pos.view', compact('pos', 'customer', 'iteams', 'posPayment'));
            } else {
                return redirect()->back()->with('error', __('Permission denied.'));
            }
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function invoicePosNumber()
    {
        if (Auth::user()->can('manage pos')) {
            $latest = Pos::where('created_by', '=', \Auth::user()->creatorId())->latest()->first();

            return $latest ? $latest->pos_id + 1 : 1;
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    public function report()
    {
        //get the current open shift for the user
        $warehouse = Auth::user()->warehouse_id;
        $openShift = Shift::whereNull('closed_at')->where('warehouse_id', $warehouse)->first();

        if (\Auth::user()->can('show pos')) {

            if(Auth::user()->type == "company")
            {
                $posPayments = Pos::where('created_by', '=', \Auth::user()->creatorId())
                    ->where('user_id',\Auth::user()->id)
                    ->with(['customer', 'warehouse', 'posPayment', 'createdBy'])
                    ->where('is_payment_set',0)
                    ->get();
            }

            if(\Auth::user()->type == "delivery")
            {
                $posPayments = Pos::where('created_by', '=', \Auth::user()->creatorId())
                    ->where('user_id',\Auth::user()->id)
                    ->where('shift_id',$openShift->id)
                    ->with(['customer', 'warehouse', 'posPayment', 'createdBy'])
                    ->where('is_payment_set',0)
                    ->get();
            } else{
                $posPayments = Pos::where('created_by', '=', \Auth::user()->creatorId())
                    ->where('shift_id',@$openShift->id)
                    ->with(['customer', 'warehouse', 'posPayment', 'createdBy'])
                    ->get();
            }

            // إضافة معلومات حالة الفاتورة لكل فاتورة
            foreach ($posPayments as $pos) {
                // إذا كانت الفاتورة مرتجعة أو ملغية، تعيين حالة الدفع وفقًا لذلك
                if ($pos->status_type == 'returned') {
                    $pos->payment_status = 'returned';
                } elseif ($pos->status_type == 'cancelled') {
                    $pos->payment_status = 'cancelled';
                } elseif ($pos->posPayment) {
                    $pos->payment_status = $pos->posPayment->payment_type;
                } else {
                    $pos->payment_status = 'unpaid';
                }
            }

            return view('pos.report', compact('posPayments'));
        } else {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }
    }

    /**
     * تحصيل الدفع من مندوب التوصيل
     */
    public function collectPayment(Request $request)
    {
        if (!\Auth::user()->can('show pos')) {
            return response()->json(['error' => __('Permission denied.')], 403);
        }

        try {
            $id = $request->id;
            $pos = Pos::find($id);

            if (!$pos) {
                return response()->json(['error' => __('الفاتورة غير موجودة.')], 404);
            }

            // التحقق من أن المستخدم الحالي هو مندوب التوصيل المسؤول عن هذه الفاتورة
            if (Auth::user()->type == 'delivery' && $pos->user_id != Auth::user()->id) {
                return response()->json(['error' => __('ليس لديك صلاحية تحصيل هذه الفاتورة.')], 403);
            }

            // التحقق من أن الفاتورة لم يتم تحصيلها بالفعل
            if ($pos->is_payment_set) {
                return response()->json(['error' => __('تم تحصيل هذه الفاتورة بالفعل.')], 400);
            }

            // الحصول على الوردية الحالية المفتوحة للمستخدم
            $warehouse = Auth::user()->warehouse_id;
            $openShift = Shift::whereNull('closed_at')->where('warehouse_id', $warehouse)->first();

            if (!$openShift) {
                return response()->json(['error' => __('لا توجد وردية مفتوحة. يرجى فتح وردية أولاً.')], 400);
            }

            // تحديث حالة الفاتورة
            $pos->is_payment_set = 1;
            $pos->save();

            // إضافة المبلغ إلى سجل النقد للوردية
            $amount = 0;
            if (!empty($pos->posPayment)) {
                $amount = $pos->posPayment->discount_amount;
            }

            // إنشاء سجل مالي للتحصيل
            $financialRecord = new FinancialRecord();
            $financialRecord->shift_id = $openShift->id;
            $financialRecord->amount = $amount;
            $financialRecord->type = 'delivery_collection';
            $financialRecord->description = 'تحصيل فاتورة رقم ' . $pos->pos_id . ' من مندوب التوصيل';
            $financialRecord->created_by = Auth::user()->id;
            $financialRecord->save();

            return response()->json(['success' => __('تم تحصيل الفاتورة بنجاح.')]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }



    public function barcode(Request $request)
    {
        if (\Auth::user()->can('manage pos')) {
            $user = \Auth::user();

            // جلب المستودعات المتاحة
            $warehouses = warehouse::where('created_by', '=', $user->creatorId())->get();

            // فلترة حسب المستودع
            $warehouseId = $request->input('warehouse_id');
            $onlyAvailable = $request->input('only_available');

            if ($warehouseId) {
                // جلب المنتجات من مستودع محدد
                $productServices = ProductService::join('warehouse_products', 'product_services.id', '=', 'warehouse_products.product_id')
                    ->where('warehouse_products.warehouse_id', $warehouseId)
                    ->where('product_services.created_by', '=', $user->creatorId())
                    ->select(
                        'product_services.*',
                        'warehouse_products.quantity as warehouse_quantity'
                    );

                // فلترة المنتجات المتوفرة فقط
                if ($onlyAvailable) {
                    $productServices->where('warehouse_products.quantity', '>', 0);
                }

                $productServices = $productServices->get();
            } else {
                // جلب جميع المنتجات
                $productServices = ProductService::where('created_by', '=', $user->creatorId());

                // فلترة المنتجات المتوفرة فقط (حسب الكمية العامة)
                if ($onlyAvailable) {
                    $productServices->where('quantity', '>', 0);
                }

                $productServices = $productServices->get();
            }

            $barcode = [
                'barcodeType' => $user->barcodeType() ?: 'code128',
                'barcodeFormat' => $user->barcodeFormat() ?: 'css',
            ];

            return view('pos.barcode', compact('productServices', 'barcode', 'warehouses'));
        } else {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

    }

    public function barcodePdf(Request $request)
    {
        if (!\Auth::user()->can('manage pos')) {
            return response()->json(['error' => 'Permission Denied'], 403);
        }

        try {
            $user = \Auth::user();

            // فلترة حسب المستودع (نفس منطق method barcode)
            $warehouseId = $request->input('warehouse_id');
            $onlyAvailable = $request->input('only_available');

            if ($warehouseId) {
                // جلب المنتجات من مستودع محدد
                $productServices = ProductService::join('warehouse_products', 'product_services.id', '=', 'warehouse_products.product_id')
                    ->where('warehouse_products.warehouse_id', $warehouseId)
                    ->where('product_services.created_by', '=', $user->creatorId())
                    ->select(
                        'product_services.*',
                        'warehouse_products.quantity as warehouse_quantity'
                    );

                // فلترة المنتجات المتوفرة فقط
                if ($onlyAvailable) {
                    $productServices->where('warehouse_products.quantity', '>', 0);
                }

                $productServices = $productServices->get();
            } else {
                // جلب جميع المنتجات
                $productServices = ProductService::where('created_by', '=', $user->creatorId());

                // فلترة المنتجات المتوفرة فقط
                if ($onlyAvailable) {
                    $productServices->where('quantity', '>', 0);
                }

                $productServices = $productServices->get();
            }

            // التحقق من وجود منتجات وإنشاء منتجات تجريبية إذا لم توجد
            if ($productServices->isEmpty()) {
                // إنشاء منتجات تجريبية للاختبار
                $productServices = collect([
                    (object)[
                        'id' => 1,
                        'name' => 'منتج تجريبي 1',
                        'sku' => '12345',
                        'sale_price' => 10.00,
                        'tax_id' => null,
                        'taxRate' => function() { return 0; }
                    ],
                    (object)[
                        'id' => 2,
                        'name' => 'منتج تجريبي 2',
                        'sku' => '67890',
                        'sale_price' => 25.50,
                        'tax_id' => null,
                        'taxRate' => function() { return 0; }
                    ],
                    (object)[
                        'id' => 3,
                        'name' => 'منتج تجريبي 3',
                        'sku' => '11111',
                        'sale_price' => 15.75,
                        'tax_id' => null,
                        'taxRate' => function() { return 0; }
                    ]
                ]);
            }

            $barcode = [
                'barcodeType' => $user->barcodeType() ?: 'code128',
                'barcodeFormat' => $user->barcodeFormat() ?: 'css',
            ];

            // إنشاء باركود بسيط جداً متوافق مع mPDF
            foreach ($productServices as $product) {
                if (!empty($product->sku)) {
                    $sku = $product->sku;

                    // باركود بسيط كنص مع خطوط
                    $barcodeHtml = '<div style="text-align: center;">';
                    $barcodeHtml .= '<div style="font-family: monospace; font-size: 6px; letter-spacing: 0px; line-height: 1; margin-bottom: 2px;">';

                    // إنشاء خطوط بسيطة
                    for($i = 0; $i < strlen($sku) * 2; $i++) {
                        $barcodeHtml .= '|';
                    }

                    $barcodeHtml .= '</div>';
                    $barcodeHtml .= '<div style="font-size: 8px;">' . $sku . '</div>';
                    $barcodeHtml .= '</div>';

                    $product->barcode_html = $barcodeHtml;
                } else {
                    $product->barcode_html = '<div style="text-align: center; font-size: 8px;">لا يوجد رمز منتج</div>';
                }
            }

            // إعداد بيانات الشركة
            $settings = Utility::settings();
            $logo_path = \App\Models\Utility::get_file('uploads/logo');
            $company_logo = \App\Models\Utility::GetLogo();

            $companyData = [
                'company_name' => $settings['company_name'] ?? 'اسم الشركة',
                'company_address' => $settings['company_address'] ?? '',
                'company_city' => $settings['company_city'] ?? '',
                'company_state' => $settings['company_state'] ?? '',
                'company_zipcode' => $settings['company_zipcode'] ?? '',
                'company_country' => $settings['company_country'] ?? '',
                'company_telephone' => $settings['company_telephone'] ?? '',
                'company_email' => $settings['company_email'] ?? '',
                'logo_path' => $logo_path,
                'company_logo' => $company_logo,
            ];

            // إنشاء محتوى HTML
            $html = view('pos.barcode_pdf_basic', compact('productServices', 'barcode', 'companyData', 'user'))->render();

            // Debug: تسجيل معلومات للتحقق
            \Log::info('PDF Generation Debug', [
                'products_count' => $productServices->count(),
                'html_length' => strlen($html),
                'first_product' => $productServices->first() ? $productServices->first()->name : 'none'
            ]);

            // إنشاء PDF باستخدام mPDF مع إعدادات مبسطة
            $mpdf = new Mpdf([
                'mode' => 'utf-8',
                'format' => 'A4',
                'orientation' => 'P',
                'margin_left' => 10,
                'margin_right' => 10,
                'margin_top' => 10,
                'margin_bottom' => 10,
            ]);

            $mpdf->WriteHTML($html);

            $filename = 'barcode_products_' . date('Y-m-d_H-i-s') . '.pdf';

            return response($mpdf->Output($filename, 'D'))
                ->header('Content-Type', 'application/pdf')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');

        } catch (\Exception $e) {
            \Log::error('خطأ في إنشاء PDF الباركود', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ], 500);
        }
    }

    public function barcodeHtml(Request $request)
    {
        if (!\Auth::user()->can('manage pos')) {
            return response()->json(['error' => 'Permission Denied'], 403);
        }

        try {
            $user = \Auth::user();

            // فلترة حسب المستودع (نفس منطق method barcode)
            $warehouseId = $request->input('warehouse_id');
            $onlyAvailable = $request->input('only_available');

            if ($warehouseId) {
                // جلب المنتجات من مستودع محدد
                $productServices = ProductService::join('warehouse_products', 'product_services.id', '=', 'warehouse_products.product_id')
                    ->where('warehouse_products.warehouse_id', $warehouseId)
                    ->where('product_services.created_by', '=', $user->creatorId())
                    ->select(
                        'product_services.*',
                        'warehouse_products.quantity as warehouse_quantity'
                    );

                // فلترة المنتجات المتوفرة فقط
                if ($onlyAvailable) {
                    $productServices->where('warehouse_products.quantity', '>', 0);
                }

                $productServices = $productServices->get();
            } else {
                // جلب جميع المنتجات
                $productServices = ProductService::where('created_by', '=', $user->creatorId());

                // فلترة المنتجات المتوفرة فقط
                if ($onlyAvailable) {
                    $productServices->where('quantity', '>', 0);
                }

                $productServices = $productServices->get();
            }

            // التحقق من وجود منتجات وإنشاء منتجات تجريبية إذا لم توجد
            if ($productServices->isEmpty()) {
                // إنشاء منتجات تجريبية للاختبار
                $productServices = collect([
                    (object)[
                        'id' => 1,
                        'name' => 'منتج تجريبي 1',
                        'sku' => '12345',
                        'sale_price' => 10.00,
                        'tax_id' => null,
                    ],
                    (object)[
                        'id' => 2,
                        'name' => 'منتج تجريبي 2',
                        'sku' => '67890',
                        'sale_price' => 25.50,
                        'tax_id' => null,
                    ],
                    (object)[
                        'id' => 3,
                        'name' => 'منتج تجريبي 3',
                        'sku' => '11111',
                        'sale_price' => 15.75,
                        'tax_id' => null,
                    ]
                ]);
            }

            $barcode = [
                'barcodeType' => $user->barcodeType() ?: 'code128',
                'barcodeFormat' => $user->barcodeFormat() ?: 'css',
            ];

            // إنشاء باركود محسن لكل منتج (مشابه للنموذج الأصلي)
            foreach ($productServices as $product) {
                if (!empty($product->sku)) {
                    $sku = $product->sku;
                    $barcodeHtml = '<div style="padding: 0px; overflow: auto; width: 102px; margin: 0 auto;">';

                    // إنشاء خطوط الباركود بأعراض مختلفة
                    $patterns = [1, 1, 4, 1, 1, 1, 1, 4, 1, 1, 1, 1, 2, 2, 2, 2, 3, 1, 2, 2, 1, 2, 1, 1, 2, 1, 1, 3, 1, 1, 1, 4, 1];

                    foreach ($patterns as $width) {
                        $barcodeHtml .= '<div style="float: left; font-size: 0px; width:0; border-left: ' . $width . 'px solid #000000; height: 40px;"></div>';
                        $barcodeHtml .= '<div style="float: left; font-size: 0px; background-color: #FFFFFF; height: 40px; width: 1px"></div>';
                    }

                    $barcodeHtml .= '<div style="clear:both; width: 100%; background-color: #FFFFFF; color: #000000; text-align: center; font-size: 10px; margin-top: 5px;">' . $sku . '</div>';
                    $barcodeHtml .= '</div>';

                    $product->barcode_html = $barcodeHtml;
                } else {
                    $product->barcode_html = '<div style="text-align: center; padding: 10px; font-size: 8px; color: #999;">لا يوجد رمز منتج</div>';
                }
            }

            // إعداد بيانات الشركة
            $settings = Utility::settings();
            $logo_path = \App\Models\Utility::get_file('uploads/logo');
            $company_logo = \App\Models\Utility::GetLogo();

            $companyData = [
                'company_name' => $settings['company_name'] ?? 'اسم الشركة',
                'logo_path' => $logo_path,
                'company_logo' => $company_logo,
            ];

            // عرض HTML مباشرة للاختبار
            return view('pos.barcode_pdf_simple', compact('productServices', 'barcode', 'companyData', 'user'));

        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ], 500);
        }
    }

    public function setting()
    {
        if (\Auth::user()->can('manage pos')) {
            $settings = Utility::settings();

            return view('pos.setting', compact('settings'));
        } else {
            return redirect()->back()->with('error', 'Permission denied.');
        }

    }

    public function BarcodesettingStore(Request $request)
    {
        $request->validate(
            [
                'barcode_type' => 'required',
                'barcode_format' => 'required',
            ]
        );

        $post['barcode_type'] = $request->barcode_type;
        $post['barcode_format'] = $request->barcode_format;

        foreach ($post as $key => $data) {

            $arr = [
                $data,
                $key,
                \Auth::user()->id,
            ];

            \DB::insert(
                'insert into settings (`value`, `name`,`created_by`) values (?, ?, ?) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`) ', $arr
            );
        }
        return redirect()->back()->with('success', 'Barcode setting successfully updated.');

    }

    public function printBarcode()
    {
        if (\Auth::user()->can('manage pos')) {
            $warehouses = warehouse::select('*', \DB::raw("CONCAT(name) AS name"))->where('created_by', \Auth::user()->creatorId())->get()->pluck('name', 'id');

            return view('pos.print', compact('warehouses'));
        } else {
            return redirect()->back()->with('error', __('Permission Denied.'));
        }

    }

    public function getproduct(Request $request)
    {
        //dd($request->all());
        if ($request->warehouse_id == 0) {
            $productServices = WarehouseProduct::where('product_id', '=', $request->warehouse_id)->where('created_by', '=', \Auth::user()->creatorId())->get()->pluck('name', 'id')->toArray();
        } else {
            $productServicesId = WarehouseProduct::where('created_by', '=', \Auth::user()->creatorId())->where('warehouse_id', $request->warehouse_id)->get()->pluck('product_id')->toArray();
            $productServices = ProductService::whereIn('id', $productServicesId)->get()->pluck('name', 'id')->toArray();
        }

        return response()->json($productServices);
    }

    public function receipt(Request $request)
    {
        if (!empty($request->product_id)) {
            $productServices = ProductService::whereIn('id', $request->product_id)->get();
            $quantity = $request->quantity;
            $barcode = [
                'barcodeType' => Auth::user()->barcodeType() == '' ? 'code128' : Auth::user()->barcodeType(),
                'barcodeFormat' => Auth::user()->barcodeFormat() == '' ? 'css' : Auth::user()->barcodeFormat(),
            ];
        } else {
            return redirect()->back()->with('error', 'Product is required.');

        }

        return view('pos.receipt', compact('productServices', 'barcode', 'quantity'));

    }

    public function cartdiscount(Request $request)
    {

        if ($request->discount) {
            $sess = session()->get('pos');
            $subtotal = !empty($sess) ? array_sum(array_column($sess, 'subtotal')) : 0;
            $discount = $request->discount;
            $total = $subtotal - $discount;
            $total = \Auth::user()->priceFormat($total);

        } else {
            $sess = session()->get('pos');
            $subtotal = !empty($sess) ? array_sum(array_column($sess, 'subtotal')) : 0;
            $discount = 0;
            $total = $subtotal - $discount;
            $total = \Auth::user()->priceFormat($total);
        }

        return response()->json(['total' => $total], '200');

    }





    public function pos($pos_id)
    {
        $settings = Utility::settings();
        $posId = Crypt::decrypt($pos_id);
        $pos = Pos::where('id', $posId)->first();

        $posPayment = PosPayment::where('pos_id', $pos->id)->first();

        $data = DB::table('settings');
        $data = $data->where('created_by', '=', $pos->created_by);
        $data1 = $data->get();

        foreach ($data1 as $row) {
            $settings[$row->name] = $row->value;
        }

        $customer = $pos->customer;

        $totalTaxPrice = 0;
        $totalQuantity = 0;
        $totalRate = 0;
        $totalDiscount = 0;
        $taxesData = [];
        $items = [];

        foreach ($pos->items as $product) {

            $item = new \stdClass();
            $item->name = !empty($product->product) ? $product->product->name : '';
            $item->quantity = $product->quantity;
            $item->tax = $product->tax;
            $item->discount = $product->discount;
            $item->price = $product->price;
            $item->description = $product->description;
            $totalQuantity += $item->quantity;
            $totalRate += $item->price;
            $totalDiscount += $item->discount;
            $taxes = Utility::tax($product->tax);
            $itemTaxes = [];
            if (!empty($item->tax)) {
                foreach ($taxes as $tax) {
                    $taxPrice = Utility::taxRate($tax->rate, $item->price, $item->quantity);
                    $totalTaxPrice += $taxPrice;

                    $itemTax['name'] = $tax->name;
                    $itemTax['rate'] = $tax->rate . '%';
                    $itemTax['price'] = Utility::priceFormat($settings, $taxPrice);
                    $itemTaxes[] = $itemTax;

                    if (array_key_exists($tax->name, $taxesData)) {
                        $taxesData[$tax->name] = $taxesData[$tax->name] + $taxPrice;
                    } else {
                        $taxesData[$tax->name] = $taxPrice;
                    }

                }

                $item->itemTax = $itemTaxes;
            } else {
                $item->itemTax = [];
            }
            $items[] = $item;
        }

        $pos->itemData = $items;
        $pos->totalTaxPrice = $totalTaxPrice;
        $pos->totalQuantity = $totalQuantity;
        $pos->totalRate = $totalRate;
        $pos->totalDiscount = $totalDiscount;
        $pos->taxesData = $taxesData;

        $logo = asset(Storage::url('uploads/logo/'));
        $company_logo = Utility::getValByName('company_logo_dark');
        $pos_logo = Utility::getValByName('pos_logo');
        if (isset($pos_logo) && !empty($pos_logo)) {
            $img = Utility::get_file('pos_logo/') . $pos_logo;
        } else {
            $img = asset($logo . '/' . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-dark.png'));
        }

        if ($pos) {
            $color = '#' . $settings['pos_color'];
            $font_color = Utility::getFontColor($color);

            return view('pos.templates.' . $settings['pos_template'], compact('pos', 'posPayment', 'color', 'settings', 'customer', 'img', 'font_color'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

    }

    public function previewPos($template, $color)
    {

        $objUser = \Auth::user();
        $settings = Utility::settings();

        $pos = new Pos();
        $posPayment = new posPayment();
        $posPayment->amount = 360;
        $posPayment->discount = 100;

        $customer = new \stdClass();
        $customer->email = '<Email>';
        $customer->shipping_name = '<Customer Name>';
        $customer->shipping_country = '<Country>';
        $customer->shipping_state = '<State>';
        $customer->shipping_city = '<City>';
        $customer->shipping_phone = '<Customer Phone Number>';
        $customer->shipping_zip = '<Zip>';
        $customer->shipping_address = '<Address>';
        $customer->billing_name = '<Customer Name>';
        $customer->billing_country = '<Country>';
        $customer->billing_state = '<State>';
        $customer->billing_city = '<City>';
        $customer->billing_phone = '<Customer Phone Number>';
        $customer->billing_zip = '<Zip>';
        $customer->billing_address = '<Address>';

        $totalTaxPrice = 0;
        $taxesData = [];
        $items = [];
        for ($i = 1; $i <= 3; $i++) {
            $item = new \stdClass();
            $item->name = 'Item ' . $i;
            $item->quantity = 1;
            $item->tax = 5;
            $item->discount = 50;
            $item->price = 100;

            $taxes = [
                'Tax 1',
                'Tax 2',
            ];

            $itemTaxes = [];
            foreach ($taxes as $k => $tax) {
                $taxPrice = 10;
                $totalTaxPrice += $taxPrice;
                $itemTax['name'] = 'Tax ' . $k;
                $itemTax['rate'] = '10 %';
                $itemTax['price'] = '$10';
                $itemTaxes[] = $itemTax;
                if (array_key_exists('Tax ' . $k, $taxesData)) {
                    $taxesData['Tax ' . $k] = $taxesData['Tax 1'] + $taxPrice;
                } else {
                    $taxesData['Tax ' . $k] = $taxPrice;
                }
            }
            $item->itemTax = $itemTaxes;
            $items[] = $item;
        }

        $pos->pos_id = 1;

        $pos->issue_date = date('Y-m-d H:i:s');
        $pos->itemData = $items;

        $pos->totalTaxPrice = 60;
        $pos->totalQuantity = 3;
        $pos->totalRate = 300;
        $pos->totalDiscount = 10;
        $pos->taxesData = $taxesData;
        $pos->created_by = $objUser->creatorId();

        $preview = 1;
        $color = '#' . $color;
        $font_color = Utility::getFontColor($color);

        $logo = asset(Storage::url('uploads/logo/'));

        $company_logo = Utility::getValByName('company_logo_dark');
        $settings_data = \App\Models\Utility::settingsById($pos->created_by);
        $pos_logo = $settings_data['pos_logo'];

        if (isset($pos_logo) && !empty($pos_logo)) {
            $img = Utility::get_file('pos_logo/') . $pos_logo;
        } else {
            $img = asset($logo . '/' . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-dark.png'));
        }

        return view('pos.templates.' . $template, compact('pos', 'preview', 'color', 'img', 'settings', 'customer', 'font_color', 'posPayment'));
    }

    public function savePosTemplateSettings(Request $request)
    {

        $post = $request->all();
        unset($post['_token']);

        if (isset($post['pos_template']) && (!isset($post['pos_color']) || empty($post['pos_color']))) {
            $post['pos_color'] = "ffffff";
        }

        if ($request->pos_logo) {
            $dir = 'pos_logo/';
            $pos_logo = \Auth::user()->id . '_pos_logo.png';
            $validation = [
                'mimes:' . 'png',
                'max:' . '20480',
            ];
            $path = Utility::upload_file($request, 'pos_logo', $pos_logo, $dir, $validation);
            if ($path['flag'] == 0) {
                return redirect()->back()->with('error', __($path['msg']));
            }
            $post['pos_logo'] = $pos_logo;
        }

        foreach ($post as $key => $data) {
            \DB::insert(
                'insert into settings (`value`, `name`,`created_by`) values (?, ?, ?) ON DUPLICATE KEY UPDATE `value` = VALUES(`value`) ', [
                    $data,
                    $key,
                    \Auth::user()->creatorId(),
                ]
            );
        }

        return redirect()->back()->with('success', __('POS Setting updated successfully'));
    }

    //for thermal print
    public function printView(Request $request)
    {

        $sess = session()->get('pos');

        $user = Auth::user();
        $settings = Utility::settings();

        $customer = Customer::where('name', '=', $request->vc_name)->where('created_by', $user->creatorId())->first();
        $warehouse = warehouse::where('id', '=', $request->warehouse_name)->where('created_by', $user->creatorId())->first();

        $details = [
            'pos_id' => $user->posNumberFormat($this->invoicePosNumber()),
            'customer' => $customer != null ? $customer->toArray() : [],
            'warehouse' => $warehouse != null ? $warehouse->toArray() : [],
            'user' => $user != null ? $user->toArray() : [],
            'date' => date('Y-m-d'),
            'pay' => 'show',
        ];

        if (!empty($details['customer'])) {
            $warehousedetails = '<h7 class="text-dark">' . ucfirst($details['warehouse']['name']) . '</p></h7>';
            $details['customer']['billing_state'] = $details['customer']['billing_state'] != '' ? ", " . $details['customer']['billing_state'] : '';
            $details['customer']['shipping_state'] = $details['customer']['shipping_state'] != '' ? ", " . $details['customer']['shipping_state'] : '';
            $customerdetails = '<h6 class="text-dark">' . ucfirst($details['customer']['name']) . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['billing_phone'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['billing_address'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['billing_city'] . $details['customer']['billing_state'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['billing_country'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['billing_zip'] . '</p></h6>';
            $shippdetails = '<h6 class="text-dark"><b>' . ucfirst($details['customer']['name']) . '</b>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['shipping_phone'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['shipping_address'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['shipping_city'] . $details['customer']['shipping_state'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['shipping_country'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['shipping_zip'] . '</p></h6>';

        } else {
            $customerdetails = '<h2 class="h6"><b>' . __('Walk-in Customer') . '</b><h2>';
            $warehousedetails = '<h7 class="text-dark">' . ucfirst($details['warehouse']['name']) . '</p></h7>';
            $shippdetails = '-';

        }

        $settings['company_telephone'] = $settings['company_telephone'] != '' ? ", " . $settings['company_telephone'] : '';
        $settings['company_state'] = $settings['company_state'] != '' ? ", " . $settings['company_state'] : '';

        $userdetails = '<h6 class="text-dark"><b>' . ucfirst($details['user']['name']) . ' </b> <h2  class="font-weight-normal">' . '<p class="m-0 font-weight-normal">' . $settings['company_name'] . $settings['company_telephone'] . '</p>' . '<p class="m-0 font-weight-normal">' . $settings['company_address'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $settings['company_city'] . $settings['company_state'] . '</p>' . '<p class="m-0 font-weight-normal">' . $settings['company_country'] . '</p>' . '<p class="m-0 font-weight-normal">' . $settings['company_zipcode'] . '</p></h2>';

        $details['customer']['details'] = $customerdetails;
        $details['warehouse']['details'] = $warehousedetails;
            //
        $details['customer']['shippdetails'] = $shippdetails;

        $details['user']['details'] = $userdetails;

        $mainsubtotal = 0;
        $totalTax = 0;
        $sales = [];

        foreach ($sess as $key => $value) {
            // التحقق من وجود البيانات المطلوبة
            if (!isset($value['name']) || !isset($value['price']) || !isset($value['quantity'])) {
                continue; // تخطي هذا العنصر إذا كانت البيانات ناقصة
            }

            // حساب المجموع الفرعي للمنتج (السعر × الكمية)
            $subtotal = $value['price'] * $value['quantity'];

            // حساب الضريبة للمنتج
            $tax_rate = isset($value['tax']) ? $value['tax'] : 0;
            $tax = ($subtotal * $tax_rate) / 100;
            $totalTax += $tax;

            $sales['data'][$key]['name'] = $value['name'];
            $sales['data'][$key]['quantity'] = $value['quantity'];
            $sales['data'][$key]['price'] = Auth::user()->priceFormat($value['price']);
            $sales['data'][$key]['tax'] = $tax_rate . '%';
            $sales['data'][$key]['tax_rate'] = $tax_rate . '%'; // إضافة معدل الضريبة
            $sales['data'][$key]['product_tax'] = isset($value['product_tax']) ? $value['product_tax'] : '';
            $sales['data'][$key]['tax_amount'] = Auth::user()->priceFormat($tax);
            $sales['data'][$key]['subtotal'] = Auth::user()->priceFormat(isset($value['subtotal']) ? $value['subtotal'] : $subtotal);
            $mainsubtotal += $subtotal; // استخدام subtotal بدلاً من value['subtotal'] للحصول على المجموع الفرعي قبل الضريبة
        }

        // حساب المجموع الفرعي (قبل الضريبة)
        $sales['subtotal'] = $mainsubtotal;

        // حساب إجمالي الضريبة
        $sales['tax'] = $totalTax;

        $discount = !empty($request->discount) ? $request->discount : 0;
        $sales['discount'] = Auth::user()->priceFormat($discount);

        // حساب الإجمالي النهائي (المجموع الفرعي + الضريبة - الخصم)
        $total = $mainsubtotal + $totalTax - $discount;

        $sales['sub_total'] = Auth::user()->priceFormat($mainsubtotal);
        $sales['tax_format'] = Auth::user()->priceFormat($totalTax);
        $sales['total'] = Auth::user()->priceFormat($total);

        //for barcode

        $productServices = ProductService::where('created_by', '=', \Auth::user()->creatorId())->get();
        $barcode = [
            'barcodeType' => Auth::user()->barcodeType(),
            'barcodeFormat' => Auth::user()->barcodeFormat(),
        ];

        // البحث عن آخر فاتورة تم إنشاؤها للمستخدم الحالي
        $latest_pos = Pos::where('created_by', '=', \Auth::user()->creatorId())
                         ->orderBy('id', 'desc')
                         ->first();

        $pos_id = $latest_pos ? $latest_pos->id : null;

        return view('pos.printview', compact('details', 'sales', 'customer', 'productServices', 'barcode', 'pos_id'));

    }

    public function posBillType(Request $request)
    {
        try {
            $sess = session()->get('pos');

            if (Auth::user()->can('manage pos') && isset($sess) && !empty($sess) && count($sess) > 0) {

                $user = Auth::user();
                $settings = Utility::settings();

                // التحقق من البيانات المطلوبة
                if (!$request->vc_name || !$request->warehouse_name) {
                    return response()->json([
                        'error' => __('Please select customer and warehouse first!')
                    ], 400);
                }

                // البحث عن العميل بالـ ID بدلاً من الاسم
                $customer = Customer::where('id', '=', $request->vc_name)->where('created_by', $user->creatorId())->first();
                $warehouse = warehouse::where('id', '=', $request->warehouse_name)->where('created_by', $user->creatorId())->first();


            $details = [
                'pos_id' => $user->posNumberFormat($this->invoicePosNumber()),
                'customer' => $customer != null ? $customer->toArray() : [],
                'warehouse' => $warehouse != null ? $warehouse->toArray() : [],
                'user' => $user != null ? $user->toArray() : [],
                'date' => date('Y-m-d'),
                'pay' => 'show',
            ];

            if (!empty($details['customer'])) {
                $warehousedetails = '<h7 class="text-dark">' . ucfirst($details['warehouse']['name']) . '</p></h7>';
                $details['customer']['billing_state'] = $details['customer']['billing_state'] != '' ? ", " . $details['customer']['billing_state'] : '';
                $details['customer']['shipping_state'] = $details['customer']['shipping_state'] != '' ? ", " . $details['customer']['shipping_state'] : '';

                $customerdetails = '<h6 class="text-dark">' . ucfirst($details['customer']['name']) . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['billing_phone'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['billing_address'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['billing_city'] . $details['customer']['billing_state'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['billing_country'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['billing_zip'] . '</p></h6>';

                $shippdetails = '<h6 class="text-dark"><b>' . ucfirst($details['customer']['name']) . '</b>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['shipping_phone'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['shipping_address'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['shipping_city'] . $details['customer']['shipping_state'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['shipping_country'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $details['customer']['shipping_zip'] . '</p></h6>';

            } else {
                $customerdetails = '<h2 class="h6"><b>' . __('Walk-in Customer') . '</b><h2>';
                $warehousedetails = '<h7 class="text-dark">' . ucfirst($details['warehouse']['name']) . '</p></h7>';
                $shippdetails = '-';

            }

            $settings['company_telephone'] = $settings['company_telephone'] != '' ? ", " . $settings['company_telephone'] : '';
            $settings['company_state'] = $settings['company_state'] != '' ? ", " . $settings['company_state'] : '';

            $userdetails = '<h6 class="text-dark"><b>' . ucfirst($details['user']['name']) . ' </b> <h2  class="font-weight-normal">' . '<p class="m-0 font-weight-normal">' . $settings['company_name'] . $settings['company_telephone'] . '</p>' . '<p class="m-0 font-weight-normal">' . $settings['company_address'] . '</p>' . '<p class="m-0 h6 font-weight-normal">' . $settings['company_city'] . $settings['company_state'] . '</p>' . '<p class="m-0 font-weight-normal">' . $settings['company_country'] . '</p>' . '<p class="m-0 font-weight-normal">' . $settings['company_zipcode'] . '</p></h2>';

            $details['customer']['details'] = $customerdetails;
            $details['warehouse']['details'] = $warehousedetails;
            //
            $details['customer']['shippdetails'] = $shippdetails;

            $details['user']['details'] = $userdetails;

            $mainsubtotal = 0;
            $totalTax = 0;
            $sales = [];

            foreach ($sess as $key => $value) {
                // التحقق من وجود البيانات المطلوبة
                if (!isset($value['name']) || !isset($value['price']) || !isset($value['quantity'])) {
                    continue; // تخطي هذا العنصر إذا كانت البيانات ناقصة
                }

                // حساب المجموع الفرعي للمنتج (السعر × الكمية)
                $subtotal = $value['price'] * $value['quantity'];

                // حساب الضريبة للمنتج
                $tax_rate = isset($value['tax']) ? $value['tax'] : 0;
                $tax = ($subtotal * $tax_rate) / 100;
                $totalTax += $tax;

                $sales['data'][$key]['name'] = $value['name'];
                $sales['data'][$key]['quantity'] = $value['quantity'];
                $sales['data'][$key]['price'] = Auth::user()->priceFormat($value['price']);
                $sales['data'][$key]['tax'] = $tax_rate . '%';
                $sales['data'][$key]['tax_rate'] = $tax_rate . '%'; // إضافة معدل الضريبة
                $sales['data'][$key]['product_tax'] = isset($value['product_tax']) ? $value['product_tax'] : '';
                $sales['data'][$key]['tax_amount'] = Auth::user()->priceFormat($tax);
                $sales['data'][$key]['subtotal'] = Auth::user()->priceFormat(isset($value['subtotal']) ? $value['subtotal'] : $subtotal);
                $mainsubtotal += $subtotal; // استخدام subtotal بدلاً من value['subtotal'] للحصول على المجموع الفرعي قبل الضريبة
            }

            // حساب المجموع الفرعي (قبل الضريبة)
            $sales['subtotal'] = $mainsubtotal;

            // حساب إجمالي الضريبة
            $sales['tax'] = $totalTax;

            $discount = !empty($request->discount) ? $request->discount : 0;
            $sales['discount'] = Auth::user()->priceFormat($discount);

            // حساب الإجمالي النهائي (المجموع الفرعي + الضريبة - الخصم)
            $total = $mainsubtotal + $totalTax - $discount;

            $sales['sub_total'] = Auth::user()->priceFormat($mainsubtotal);
            $sales['tax_format'] = Auth::user()->priceFormat($totalTax);
            // تطبيق تنسيق الأرقام العشرية على القيمة الإجمالية لضمان التطابق مع العرض
            $sales['total'] = round($total, 2);

            // البحث عن آخر فاتورة تم إنشاؤها للمستخدم الحالي
            $latest_pos = Pos::where('created_by', '=', \Auth::user()->creatorId())
                             ->orderBy('id', 'desc')
                             ->first();

            $pos_id = $latest_pos ? $latest_pos->id : null;

            return view('pos.bill_type', compact('sales', 'details', 'pos_id'));
        } else {
            return response()->json(
                [
                    'error' => __('Add some products to cart!'),
                ],
                '404'
            );
        }
        } catch (\Exception $e) {
            \Log::error('POS Bill Type Error: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'request_data' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => __('An error occurred while loading payment options. Please try again.')
            ], 500);
        }
    }

    public function deleveryBillType(Request $request)
    {

        if (Auth::user()->can('manage delevery')) {
            // تطبيق تنسيق الأرقام العشرية على القيمة الإجمالية لضمان التطابق مع العرض
            $sales['total'] = round($request['total_price'], 2);
            $sales['pos_id'] = $request['pos_id'];
            return view('pos.bill_type_delivery', compact('sales'));
        } else {
            return response()->json(
                [
                    'error' => __('You are not authorized to perform this action'),
                ],
                '401'
            );
        }
    }

    /**
     * عرض صفحة إرجاع المنتجات
     *
     * @param int $id معرف فاتورة نقطة البيع
     * @return \Illuminate\Http\Response
     */
    public function returnProducts($id)
    {
        if (\Auth::user()->can('manage pos')) {
            $pos = Pos::find($id);

            if (!$pos || $pos->created_by != \Auth::user()->creatorId()) {
                return redirect()->back()->with('error', __('Permission denied.'));
            }

            $customer = $pos->customer;
            $items = $pos->items;

            return view('pos.return', compact('pos', 'customer', 'items'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    /**
     * معالجة عملية إرجاع المنتجات
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id معرف فاتورة نقطة البيع
     * @return \Illuminate\Http\Response
     */
    public function processReturn(Request $request, $id)
    {
        if (\Auth::user()->can('manage pos')) {
            $pos = Pos::find($id);

            if (!$pos || $pos->created_by != \Auth::user()->creatorId()) {
                return redirect()->back()->with('error', __('Permission denied.'));
            }

            // إنشاء سجل إرجاع جديد
            $posReturn = new PosReturn();
            $posReturn->pos_id = $pos->id;
            $posReturn->return_date = date('Y-m-d');
            $posReturn->return_note = $request->return_note;
            $posReturn->created_by = \Auth::user()->id;
            $posReturn->save();

            $returnItems = $request->return_items;
            $returnQty = $request->return_qty;
            $returnReason = $request->return_reason;

            // حفظ تفاصيل المنتجات المرجعة
            if (!empty($returnItems)) {
                foreach ($returnItems as $key => $item_id) {
                    if (isset($returnQty[$key]) && $returnQty[$key] > 0) {
                        $posProduct = PosProduct::find($item_id);

                        // إنشاء سجل للمنتج المرجع
                        $returnItem = new PosReturnItem();
                        $returnItem->pos_return_id = $posReturn->id;
                        $returnItem->pos_product_id = $posProduct->id;
                        $returnItem->product_id = $posProduct->product_id;
                        $returnItem->quantity = $returnQty[$key];
                        $returnItem->price = $posProduct->price;
                        $returnItem->tax = $posProduct->tax;
                        $returnItem->return_reason = $returnReason[$key] ?? '';
                        $returnItem->save();

                        // تحديث المخزون
                        Utility::warehouse_quantity('plus', $returnQty[$key], $posProduct->product_id, $pos->warehouse_id);

                        // تحديث تقرير المخزون
                        $type = 'pos_return';
                        $type_id = $posReturn->id;
                        $description = $returnQty[$key] . '  ' . __(' quantity returned from pos') . ' ' . \Auth::user()->posNumberFormat($pos->pos_id);
                        Utility::addProductStock($posProduct->product_id, $returnQty[$key], $type, $description, $type_id);
                    }
                }
            }

            return redirect()->route('pos.returns.report')->with('success', __('Products returned successfully.'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    /**
     * عرض تقرير المرتجعات
     *
     * @return \Illuminate\Http\Response
     */
    public function returnsReport()
    {
        if (\Auth::user()->can('manage pos')) {
            $posReturns = PosReturn::where('created_by', '=', \Auth::user()->creatorId())->get();
            return view('pos.returns', compact('posReturns'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    /**
     * عرض تفاصيل المرتجع
     *
     * @param int $id معرف المرتجع
     * @return \Illuminate\Http\Response
     */
    public function returnView($id)
    {
        if (\Auth::user()->can('manage pos')) {
            $return = PosReturn::find($id);

            if (!$return || $return->created_by != \Auth::user()->creatorId()) {
                return redirect()->back()->with('error', __('Permission denied.'));
            }

            return view('pos.return_view', compact('return'));
        } else {
            return redirect()->back()->with('error', __('Permission denied.'));
        }
    }

    /**
     * طباعة فاتورة حرارية لفاتورة موجودة
     *
     * @param int $id معرف فاتورة نقطة البيع
     * @return \Illuminate\Http\Response
     */
    public function thermalPrint($id)
    {
        $pos = Pos::with(['customer', 'warehouse', 'items.product'])->find($id);

        if (!$pos || $pos->created_by != \Auth::user()->creatorId()) {
            return redirect()->back()->with('error', __('الفاتورة غير موجودة أو ليس لديك صلاحية للوصول إليها.'));
        }

        $user = Auth::user();
        $settings = Utility::settings();

        // تجهيز بيانات الفاتورة
        $details = [
            'pos_id' => $user->posNumberFormat($pos->pos_id),
            'customer' => $pos->customer ? $pos->customer->toArray() : [],
            'warehouse' => $pos->warehouse ? $pos->warehouse->toArray() : [],
            'user' => $user->toArray(),
            'date' => $user->dateFormat($pos->pos_date),
            'pay' => 'hide',
        ];

        // تجهيز بيانات الباركود
        $barcode = [
            'barcodeType' => $user->barcodeType(),
            'barcodeFormat' => $user->barcodeFormat(),
        ];

        // إعداد شعار الشركة
        $logo_path = \App\Models\Utility::get_file('uploads/logo');
        $company_logo = '';

        // البحث عن الشعار الداكن أولاً، ثم الفاتح، ثم الافتراضي
        if (!empty($settings['company_logo_dark'])) {
            $logo_file = $logo_path . '/' . $settings['company_logo_dark'];
            // التحقق من وجود الملف
            if (file_exists(public_path('storage/uploads/logo/' . $settings['company_logo_dark']))) {
                $company_logo = $logo_file;
            }
        } elseif (!empty($settings['company_logo_light'])) {
            $logo_file = $logo_path . '/' . $settings['company_logo_light'];
            if (file_exists(public_path('storage/uploads/logo/' . $settings['company_logo_light']))) {
                $company_logo = $logo_file;
            }
        } elseif (!empty($settings['company_logo'])) {
            $logo_file = $logo_path . '/' . $settings['company_logo'];
            if (file_exists(public_path('storage/uploads/logo/' . $settings['company_logo']))) {
                $company_logo = $logo_file;
            }
        }

        // إذا لم يوجد شعار مخصص، التحقق من وجود الشعار الافتراضي
        if (empty($company_logo)) {
            $default_logo = $logo_path . '/logo-dark.png';
            if (file_exists(public_path('storage/uploads/logo/logo-dark.png'))) {
                $company_logo = $default_logo;
            }
        }

        return view('pos.thermal_print_clean', compact('details', 'pos', 'barcode', 'settings', 'company_logo'));
    }

    /**
     * الحصول على آخر فاتورة تم إنشاؤها
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLatestPos()
    {
        try {
            $latest_pos = Pos::where('created_by', '=', \Auth::user()->creatorId())
                             ->orderBy('id', 'desc')
                             ->first();

            if ($latest_pos) {
                return response()->json([
                    'success' => true,
                    'pos_id' => $latest_pos->id,
                    'pos_number' => \Auth::user()->posNumberFormat($latest_pos->pos_id),
                    'message' => __('Latest POS found successfully.')
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => __('No POS records found.')
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Error retrieving latest POS: ') . $e->getMessage()
            ]);
        }
    }

    /**
     * جلب قائمة فواتير الشفت الحالي للطباعة الحرارية
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getInvoicesList()
    {
        try {
            if (!\Auth::user()->can('show pos')) {
                return response()->json([
                    'success' => false,
                    'message' => __('Permission denied')
                ]);
            }

            // الحصول على الشفت الحالي المفتوح للمستخدم
            $warehouse = \Auth::user()->warehouse_id;
            $openShift = Shift::with('warehouse')->whereNull('closed_at')->where('warehouse_id', $warehouse)->first();

            // جلب الفواتير - إما من الشفت الحالي أو جميع الفواتير الحديثة
            $invoicesQuery = Pos::where('created_by', '=', \Auth::user()->creatorId())
                ->with(['customer', 'warehouse', 'posPayment', 'shift'])
                ->orderBy('id', 'desc');

            if ($openShift) {
                // إذا كان هناك شفت مفتوح، اجلب فواتير الشفت فقط
                $invoicesQuery->where('shift_id', $openShift->id);
            } else {
                // إذا لم يكن هناك شفت مفتوح، اجلب آخر 50 فاتورة
                $invoicesQuery->limit(50);
            }

            // تطبيق تصفية إضافية حسب نوع المستخدم
            if (\Auth::user()->type == "delivery") {
                $invoicesQuery->where('user_id', \Auth::user()->id);
            }

            $invoices = $invoicesQuery->get();

            $invoicesList = [];
            foreach ($invoices as $invoice) {
                $invoicesList[] = [
                    'id' => $invoice->id,
                    'pos_number' => \Auth::user()->posNumberFormat($invoice->id),
                    'date' => \Auth::user()->dateFormat($invoice->pos_date),
                    'customer_name' => !empty($invoice->customer) ? $invoice->customer->name : __('Walk-in Customer'),
                    'warehouse_name' => !empty($invoice->warehouse) ? $invoice->warehouse->name : '',
                    'total' => \Auth::user()->priceFormat($invoice->getTotal()),
                    'payment_type' => $invoice->posPayment ? $invoice->posPayment->payment_type : 'cash',
                    'thermal_print_url' => route('pos.thermal.print', $invoice->id)
                ];
            }

            $shiftInfo = null;
            if ($openShift) {
                $shiftInfo = [
                    'shift_id' => $openShift->id,
                    'opened_at' => $openShift->opened_at ? $openShift->opened_at->format('Y-m-d H:i:s') : null,
                    'warehouse_name' => !empty($openShift->warehouse) ? $openShift->warehouse->name : '',
                    'total_invoices' => count($invoicesList)
                ];
            }

            return response()->json([
                'success' => true,
                'invoices' => $invoicesList,
                'shift_info' => $shiftInfo,
                'message' => $openShift ? __('Invoices from current shift') : __('Recent invoices (no open shift)')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Error retrieving invoices list: ') . $e->getMessage()
            ]);
        }
    }


}
