<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تزامن المستودع والعميل</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        .sync-status {
            padding: 10px;
            border-radius: 5px;
            margin: 5px 0;
        }
        .sync-ok { background-color: #d4edda; color: #155724; }
        .sync-error { background-color: #f8d7da; color: #721c24; }
        .sync-warning { background-color: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-10 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h3>اختبار تزامن المستودع والعميل - POS</h3>
                    </div>
                    <div class="card-body">
                        <!-- محاكاة عناصر POS -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label>اختيار العميل:</label>
                                <select id="customer" class="form-control">
                                    <option value="">اختر عميل</option>
                                    <option value="1">عميل 1</option>
                                    <option value="2">عميل 2</option>
                                    <option value="3">عميل 3</option>
                                </select>
                                <input type="hidden" id="vc_name_hidden" value="">
                            </div>
                            <div class="col-md-6">
                                <label>اختيار المستودع:</label>
                                <select id="warehouse" class="form-control">
                                    <option value="">اختر مستودع</option>
                                    <option value="1">مستودع 1</option>
                                    <option value="2">مستودع 2</option>
                                    <option value="3">مستودع 3</option>
                                </select>
                                <input type="hidden" id="warehouse_name_hidden" value="">
                            </div>
                        </div>
                        
                        <!-- حقول إضافية -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <input type="hidden" id="delivery_user_hidden" value="">
                            </div>
                            <div class="col-md-4">
                                <input type="hidden" id="discount_hidden" value="0">
                            </div>
                            <div class="col-md-4">
                                <input type="hidden" id="quotation_id" value="0">
                            </div>
                        </div>
                        
                        <!-- محاكاة جدول السلة -->
                        <div class="mb-3">
                            <h5>محتويات السلة:</h5>
                            <table class="table">
                                <tbody id="tbody">
                                    <tr id="product-id-1">
                                        <td>منتج تجريبي 1</td>
                                        <td>100 ريال</td>
                                        <td><input type="number" name="quantity" value="1"></td>
                                        <td class="subtotal">100</td>
                                    </tr>
                                    <tr id="product-id-2">
                                        <td>منتج تجريبي 2</td>
                                        <td>200 ريال</td>
                                        <td><input type="number" name="quantity" value="2"></td>
                                        <td class="subtotal">400</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- زر الدفع -->
                        <div class="text-center mb-3">
                            <button id="payment" type="button" class="btn btn-danger btn-lg" 
                                    data-ajax-popup="true"
                                    data-size="xl" 
                                    data-align="centered" 
                                    data-url="/pos-payment-type"
                                    data-title="POS Invoice">
                                <i class="fas fa-credit-card me-2"></i>دفع
                            </button>
                        </div>
                        
                        <!-- حالة التزامن -->
                        <div class="mb-3">
                            <h5>حالة التزامن:</h5>
                            <div id="sync-status"></div>
                        </div>
                        
                        <!-- أزرار الاختبار -->
                        <div class="text-center mb-3">
                            <button onclick="checkSync()" class="btn btn-info me-2">فحص التزامن</button>
                            <button onclick="debugPaymentButton()" class="btn btn-warning me-2">تشخيص زر الدفع</button>
                            <button onclick="testPayment()" class="btn btn-success">اختبار الدفع</button>
                        </div>
                        
                        <!-- منطقة اللوج -->
                        <div class="mt-4">
                            <h5>سجل الأحداث:</h5>
                            <div id="log" class="border p-3" style="height: 300px; overflow-y: auto; background: #f8f9fa;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div class="modal fade" id="commonModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- POS Payment Fix -->
    <script src="js/pos_payment_fix.js"></script>
    
    <script>
        // دالة لإضافة رسالة إلى اللوج
        function addLog(message) {
            var timestamp = new Date().toLocaleTimeString();
            $('#log').append('<div>[' + timestamp + '] ' + message + '</div>');
            $('#log').scrollTop($('#log')[0].scrollHeight);
        }

        // دالة فحص التزامن
        function checkSync() {
            addLog('=== فحص التزامن ===');
            
            var customerSelect = document.getElementById('customer');
            var warehouseSelect = document.getElementById('warehouse');
            var customerHidden = document.getElementById('vc_name_hidden');
            var warehouseHidden = document.getElementById('warehouse_name_hidden');
            
            var syncStatus = document.getElementById('sync-status');
            var statusHtml = '';
            
            // فحص العميل
            if (customerSelect && customerHidden) {
                var customerSync = customerSelect.value === customerHidden.value;
                statusHtml += '<div class="sync-status ' + (customerSync ? 'sync-ok' : 'sync-error') + '">';
                statusHtml += '<strong>العميل:</strong> ';
                statusHtml += 'المرئي: "' + customerSelect.value + '" | المخفي: "' + customerHidden.value + '" ';
                statusHtml += customerSync ? '✅ متزامن' : '❌ غير متزامن';
                statusHtml += '</div>';
                
                addLog('Customer sync: ' + (customerSync ? 'OK' : 'MISMATCH') + ' (Visible: "' + customerSelect.value + '", Hidden: "' + customerHidden.value + '")');
            }
            
            // فحص المستودع
            if (warehouseSelect && warehouseHidden) {
                var warehouseSync = warehouseSelect.value === warehouseHidden.value;
                statusHtml += '<div class="sync-status ' + (warehouseSync ? 'sync-ok' : 'sync-error') + '">';
                statusHtml += '<strong>المستودع:</strong> ';
                statusHtml += 'المرئي: "' + warehouseSelect.value + '" | المخفي: "' + warehouseHidden.value + '" ';
                statusHtml += warehouseSync ? '✅ متزامن' : '❌ غير متزامن';
                statusHtml += '</div>';
                
                addLog('Warehouse sync: ' + (warehouseSync ? 'OK' : 'MISMATCH') + ' (Visible: "' + warehouseSelect.value + '", Hidden: "' + warehouseHidden.value + '")');
            }
            
            // فحص السلة
            var cartItems = document.querySelectorAll('#tbody tr:not(.no-found)');
            statusHtml += '<div class="sync-status ' + (cartItems.length > 0 ? 'sync-ok' : 'sync-warning') + '">';
            statusHtml += '<strong>السلة:</strong> ' + cartItems.length + ' منتج';
            statusHtml += '</div>';
            
            addLog('Cart items: ' + cartItems.length);
            
            syncStatus.innerHTML = statusHtml;
        }

        // دالة اختبار الدفع
        function testPayment() {
            addLog('=== اختبار الدفع ===');
            var paymentButton = document.getElementById('payment');
            if (paymentButton) {
                addLog('محاولة النقر على زر الدفع...');
                paymentButton.click();
            } else {
                addLog('زر الدفع غير موجود!');
            }
        }

        // معالجات التغيير
        document.getElementById('customer').addEventListener('change', function() {
            addLog('Customer changed to: ' + this.value);
            setTimeout(checkSync, 100);
        });

        document.getElementById('warehouse').addEventListener('change', function() {
            addLog('Warehouse changed to: ' + this.value);
            setTimeout(checkSync, 100);
        });

        // تشغيل فحص أولي
        $(document).ready(function() {
            addLog('الصفحة جاهزة');
            setTimeout(function() {
                checkSync();
                addLog('يمكنك الآن اختبار اختيار المستودع والعميل');
            }, 1000);
        });
    </script>
</body>
</html>
