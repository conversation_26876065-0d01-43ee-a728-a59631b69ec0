<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\ProjectUser;
use App\Models\User;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\AssignProject;
use App\Models\Project;
use App\Models\Utility;
use App\Models\Tag;
use App\Models\ProjectTask;
use App\Models\TimeTracker;
use App\Models\TrackPhoto;
use App\Models\AttendanceEmployee;
use App\Models\Employee;
use App\Models\EmployeeLocation;
use App\Models\Branch;
use App\Models\Department;
use App\Models\warehouse;

use Illuminate\Support\Facades\Validator;


class ApiController extends Controller
{
    //
    use ApiResponser;

    public function login(Request $request)
    {

        $attr = $request->validate([
            'email' => 'required|string|email|',
            'password' => 'required|string'
        ]);

        if (!Auth::attempt($attr)) {
            return $this->error('Credentials not match', 401);
        }

        $settings              = Utility::settings(auth()->user()->id);

        $settings = [
            'shot_time'=> isset($settings['interval_time'])?$settings['interval_time']:0.5,
        ];

        return $this->success([
            'token' => auth()->user()->createToken('API Token')->plainTextToken,
            'user'=> auth()->user()->id,
//            'user'=> auth()->user()->id,
            'settings' =>$settings,
        ],'Login successfully.');
    }
    public function logout()
    {
        auth()->user()->tokens()->delete();
        return $this->success([],'Tokens Revoked');
    }


    public function getProjects(Request $request)
    {

        $user = auth()->user();

        if($user->type!='company')
        {
            $assign_pro_ids = ProjectUser::where('user_id',$user->id)->pluck('project_id');

//            $project_s      = Project::with('tasks')->select(
//                [
//                    'project_name',
//                    'id',
//                    'client_id',
//                ]
//            )->whereIn('id', $assign_pro_ids)->get()->toArray();

            $project_s      = Project::with('tasks')->whereIn('id', $assign_pro_ids)->get()->toArray();

        }
        else
        {

//            $project_s = Project::with('tasks')->select(
//                [
//                    'project_name',
//                    'id',
//                    'client_id',
//                ]
//            )->where('created_by', $user->id)->get()->toArray();

            $project_s = Project::with('tasks')->where('created_by', $user->id)->get()->toArray();


        }

        return $this->success([
            'projects' => $project_s,
        ],'Get Project List successfully.');
    }


    public function addTracker(Request $request){

        $user = auth()->user();
        if($request->has('action') && $request->action == 'start'){

            $validatorArray = [
                'task_id' => 'required|integer',
            ];
            $validator      = \Validator::make(
                $request->all(), $validatorArray
            );
            if($validator->fails())
            {
                return $this->error($validator->errors()->first(), 401);
            }
            $task= ProjectTask::find($request->task_id);

            if(empty($task)){
                return $this->error('Invalid task', 401);
            }

            $project_id = isset($task->project_id)?$task->project_id:'';
            TimeTracker::where('created_by', '=', $user->id)->where('is_active', '=', 1)->update(['end_time' => date("Y-m-d H:i:s")]);

            $track['name']        = $request->has('workin_on') ? $request->input('workin_on') : '';
            $track['project_id']  = $project_id;
            $track['is_billable'] =  $request->has('is_billable')? $request->is_billable:0;
            $track['tag_id']      = $request->has('workin_on') ? $request->input('workin_on') : '';
            $track['start_time']  = $request->has('time') ?  date("Y-m-d H:i:s",strtotime($request->input('time'))) : date("Y-m-d H:i:s");
            $track['task_id']     = $request->has('task_id') ? $request->input('task_id') : '';
            $track['created_by']  = $user->id;
            $track                = TimeTracker::create($track);
            $track->action        ='start';

            return $this->success( $track,'Track successfully create.');
        }else{
            $validatorArray = [
                'task_id' => 'required|integer',
                'traker_id' =>'required|integer',
            ];
            $validator      = Validator::make(
                $request->all(), $validatorArray
            );
            if($validator->fails())
            {
                return Utility::error_res($validator->errors()->first());
            }
            $tracker = TimeTracker::where('id',$request->traker_id)->first();
            // dd($tracker);
            if($tracker)
            {
                $tracker->end_time   = $request->has('time') ?  date("Y-m-d H:i:s",strtotime($request->input('time'))) : date("Y-m-d H:i:s");
                $tracker->is_active  = 0;
                $tracker->total_time = Utility::diffance_to_time($tracker->start_time, $tracker->end_time);
                $tracker->save();
                return $this->success( $tracker,'Stop time successfully.');
            }
        }

    }
    public function uploadImage(Request $request){
        $user = auth()->user();
        $image_base64 = base64_decode($request->img);
        $file =$request->imgName;
        if($request->has('tracker_id') && !empty($request->tracker_id)){
            $app_path = storage_path('uploads/traker_images/').$request->tracker_id.'/';
            if (!file_exists($app_path)) {
                mkdir($app_path, 0777, true);
            }

        }else{
            $app_path = storage_path('uploads/traker_images/');
            if (!is_dir($app_path)) {
                mkdir($app_path, 0777, true);
            }
        }
        $file_name =  $app_path.$file;
        file_put_contents( $file_name, $image_base64);
        $new = new TrackPhoto();
        $new->track_id = $request->tracker_id;
        $new->user_id  = $user->id;
        $new->img_path  = 'uploads/traker_images/'.$request->tracker_id.'/'.$file;
        $new->time  = $request->time;
        $new->status  = 1;
        $new->save();
        return $this->success( [],'Uploaded successfully.');
    }

    // Mobile App Methods for Employee Tracking

    /**
     * Get authenticated user details for mobile app
     */
    public function getUser()
    {
        $user = auth()->user();
        $employee = Employee::where('user_id', $user->id)->first();

        return $this->success([
            'user' => $user,
            'employee' => $employee,
            'permissions' => $user->getAllPermissions()->pluck('name'),
            'roles' => $user->getRoleNames()
        ], 'User data retrieved successfully.');
    }

    /**
     * Clock in for attendance
     */
    public function clockIn(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'address' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return $this->error($validator->errors()->first(), 422);
        }

        $user = auth()->user();
        $employee = Employee::where('user_id', $user->id)->first();

        if (!$employee) {
            return $this->error('Employee record not found', 404);
        }

        $date = date('Y-m-d');
        $time = date('H:i:s');

        // Check if already clocked in today
        $existingAttendance = AttendanceEmployee::where('employee_id', $employee->id)
            ->where('date', $date)
            ->first();

        if ($existingAttendance) {
            return $this->error('Already clocked in today', 400);
        }

        // Calculate late time
        $startTime = Utility::getValByName('company_start_time') ?: '09:00:00';
        $totalLateSeconds = strtotime($time) - strtotime($startTime);
        $late = $totalLateSeconds > 0 ? gmdate('H:i:s', $totalLateSeconds) : '00:00:00';

        // Create attendance record
        $attendance = AttendanceEmployee::create([
            'employee_id' => $employee->id,
            'date' => $date,
            'status' => 'Present',
            'clock_in' => $time,
            'clock_out' => '00:00:00',
            'late' => $late,
            'early_leaving' => '00:00:00',
            'overtime' => '00:00:00',
            'total_rest' => '00:00:00',
            'created_by' => $user->id
        ]);

        // Save location
        EmployeeLocation::create([
            'employee_id' => $employee->id,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'address' => $request->address,
            'location_timestamp' => now(),
            'location_type' => 'clock_in',
            'created_by' => $user->id
        ]);

        return $this->success($attendance, 'Clocked in successfully.');
    }

    /**
     * Clock out for attendance
     */
    public function clockOut(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'address' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return $this->error($validator->errors()->first(), 422);
        }

        $user = auth()->user();
        $employee = Employee::where('user_id', $user->id)->first();

        if (!$employee) {
            return $this->error('Employee record not found', 404);
        }

        $date = date('Y-m-d');
        $time = date('H:i:s');

        // Find today's attendance
        $attendance = AttendanceEmployee::where('employee_id', $employee->id)
            ->where('date', $date)
            ->where('clock_out', '00:00:00')
            ->first();

        if (!$attendance) {
            return $this->error('No clock-in record found for today', 400);
        }

        // Calculate overtime and early leaving
        $endTime = Utility::getValByName('company_end_time') ?: '17:00:00';
        $totalOvertimeSeconds = strtotime($time) - strtotime($endTime);
        $overtime = $totalOvertimeSeconds > 0 ? gmdate('H:i:s', $totalOvertimeSeconds) : '00:00:00';

        $totalEarlySeconds = strtotime($endTime) - strtotime($time);
        $earlyLeaving = $totalEarlySeconds > 0 ? gmdate('H:i:s', $totalEarlySeconds) : '00:00:00';

        // Update attendance
        $attendance->update([
            'clock_out' => $time,
            'overtime' => $overtime,
            'early_leaving' => $earlyLeaving
        ]);

        // Save location
        EmployeeLocation::create([
            'employee_id' => $employee->id,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'address' => $request->address,
            'location_timestamp' => now(),
            'location_type' => 'clock_out',
            'created_by' => $user->id
        ]);

        return $this->success($attendance, 'Clocked out successfully.');
    }

    /**
     * Get today's attendance
     */
    public function getTodayAttendance()
    {
        $user = auth()->user();
        $employee = Employee::where('user_id', $user->id)->first();

        if (!$employee) {
            return $this->error('Employee record not found', 404);
        }

        $attendance = AttendanceEmployee::where('employee_id', $employee->id)
            ->where('date', date('Y-m-d'))
            ->first();

        return $this->success($attendance, 'Today\'s attendance retrieved successfully.');
    }

    /**
     * Get attendance history
     */
    public function getAttendanceHistory(Request $request)
    {
        $user = auth()->user();
        $employee = Employee::where('user_id', $user->id)->first();

        if (!$employee) {
            return $this->error('Employee record not found', 404);
        }

        $limit = $request->get('limit', 30);
        $attendance = AttendanceEmployee::where('employee_id', $employee->id)
            ->orderBy('date', 'desc')
            ->limit($limit)
            ->get();

        return $this->success($attendance, 'Attendance history retrieved successfully.');
    }

    /**
     * Update employee location
     */
    public function updateLocation(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'address' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return $this->error($validator->errors()->first(), 422);
        }

        $user = auth()->user();
        $employee = Employee::where('user_id', $user->id)->first();

        if (!$employee) {
            return $this->error('Employee record not found', 404);
        }

        $location = EmployeeLocation::create([
            'employee_id' => $employee->id,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'address' => $request->address,
            'location_timestamp' => now(),
            'location_type' => 'tracking',
            'notes' => $request->notes,
            'created_by' => $user->id
        ]);

        return $this->success($location, 'Location updated successfully.');
    }

    /**
     * Get location history
     */
    public function getLocationHistory(Request $request)
    {
        $user = auth()->user();
        $employee = Employee::where('user_id', $user->id)->first();

        if (!$employee) {
            return $this->error('Employee record not found', 404);
        }

        $limit = $request->get('limit', 50);
        $locations = EmployeeLocation::where('employee_id', $employee->id)
            ->orderBy('location_timestamp', 'desc')
            ->limit($limit)
            ->get();

        return $this->success($locations, 'Location history retrieved successfully.');
    }

    /**
     * Get employee profile
     */
    public function getEmployeeProfile()
    {
        $user = auth()->user();
        $employee = Employee::where('user_id', $user->id)
            ->with(['branch', 'department', 'designation'])
            ->first();

        if (!$employee) {
            return $this->error('Employee record not found', 404);
        }

        return $this->success([
            'user' => $user,
            'employee' => $employee
        ], 'Employee profile retrieved successfully.');
    }

    /**
     * Update employee profile
     */
    public function updateEmployeeProfile(Request $request)
    {
        $user = auth()->user();
        $employee = Employee::where('user_id', $user->id)->first();

        if (!$employee) {
            return $this->error('Employee record not found', 404);
        }

        $validator = Validator::make($request->all(), [
            'phone' => 'nullable|string',
            'address' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return $this->error($validator->errors()->first(), 422);
        }

        $employee->update($request->only(['phone', 'address']));

        return $this->success($employee, 'Profile updated successfully.');
    }

    /**
     * Get company information
     */
    public function getCompanyInfo()
    {
        $user = auth()->user();
        $settings = Utility::settings($user->creatorId());

        return $this->success([
            'company_name' => $settings['company_name'] ?? '',
            'company_email' => $settings['company_email'] ?? '',
            'company_address' => $settings['company_address'] ?? '',
            'company_city' => $settings['company_city'] ?? '',
            'company_state' => $settings['company_state'] ?? '',
            'company_zipcode' => $settings['company_zipcode'] ?? '',
            'company_country' => $settings['company_country'] ?? '',
            'company_telephone' => $settings['company_telephone'] ?? '',
        ], 'Company information retrieved successfully.');
    }

    /**
     * Get branches
     */
    public function getBranches()
    {
        $user = auth()->user();
        $branches = Branch::where('created_by', $user->creatorId())->get();

        return $this->success($branches, 'Branches retrieved successfully.');
    }

    /**
     * Get departments
     */
    public function getDepartments()
    {
        $user = auth()->user();
        $departments = Department::where('created_by', $user->creatorId())
            ->with('branch')
            ->get();

        return $this->success($departments, 'Departments retrieved successfully.');
    }

}
