@extends('layouts.admin')

@section('page-title')
    {{ __('إدارة المنتجات والخدمات - العمليات المالية') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="#">{{ __('إدارة العمليات المالية') }}</a></li>
    <li class="breadcrumb-item">{{ __('إدارة المنتجات والخدمات') }}</li>
@endsection
@section('action-btn')
    <div class="float-end">
        @can('create product & service')
            <a href="#" data-size="lg" data-url="{{ route('financial.productservice.create') }}" data-ajax-popup="true"
               data-bs-toggle="tooltip" data-bs-original-title="{{ __('Create New Product') }}"
               data-title="{{ __('Create New Product & Service') }}" class="btn btn-sm btn-primary me-2">
                <i class="ti ti-plus"></i> {{ __('إنشاء منتج جديد') }}
            </a>
        @endcan

        @can('manage product & service')
            <a href="#" data-size="lg" data-bs-toggle="tooltip" title="{{ __('Import Products') }}"
               data-url="{{ route('financial.products.import') }}" data-ajax-popup="true"
               data-title="{{ __('Import Products CSV File') }}" class="btn btn-sm bg-brown-subtitle me-2">
                <i class="ti ti-file-import"></i> {{ __('استيراد') }}
            </a>

            <a href="{{ route('financial.products.export') }}" data-bs-toggle="tooltip" title="{{ __('Export Products') }}"
               class="btn btn-sm btn-secondary me-2">
                <i class="ti ti-file-export"></i> {{ __('تصدير') }}
            </a>
        @endcan

        <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="collapse"
                data-bs-target="#filterSection" aria-expanded="false" aria-controls="filterSection">
            <i class="ti ti-filter"></i> {{ __('فلترة') }}
        </button>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-sm-12">
            <div class="collapse mt-2" id="filterSection">
                <div class="card">
                    <div class="card-body">
                        {{ Form::open(['route' => ['financial.productservice.index'], 'method' => 'GET', 'id' => 'product_service']) }}
                        <div class="d-flex align-items-center justify-content-end">
                            <div class="col-xl-3 col-lg-3 col-md-6 me-1">
                                <div class="btn-box">
                                    {{ Form::label('category', __('Category'),['class'=>'form-label']) }}
                                    {{ Form::select('category', $category, isset($_GET['category']) ? $_GET['category'] : null, ['class' => 'form-control select','id'=>'choices-multiple', 'required' => 'required']) }}
                                </div>
                            </div>
                            <div class="col-auto float-end ms-2 mt-4">
                                <a href="#" class="btn btn-sm btn-primary me-1"
                                   onclick="document.getElementById('product_service').submit(); return false;"
                                   data-bs-toggle="tooltip" title="{{ __('apply') }}">
                                    <span class="btn-inner--icon"><i class="ti ti-search"></i></span>
                                </a>
                                <a href="{{ route('financial.productservice.index') }}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip"
                                   title="{{ __('Reset') }}">
                                    <span class="btn-inner--icon"><i class="ti ti-refresh "></i></span>
                                </a>
                            </div>

                        </div>
                        {{ Form::close() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-header card-body table-border-style">
                    <h5>{{ __('إدارة المنتجات والخدمات - العمليات المالية') }}</h5>
                    <small class="text-muted">{{ __('إدارة جميع المنتجات والخدمات مع إمكانيات التعديل المباشر') }}</small>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table datatable" id="pc-dt-simple">
                            <thead>
                            <tr>
                                <th>{{ __('اسم المنتج') }}</th>
                                <th>{{ __('الباركود') }}</th>
                                <th>{{ __('سعر البيع') }}</th>
                                <th>{{ __('سعر الشراء') }}</th>
                                <th>{{ __('حساب الدخل') }}</th>
                                <th>{{ __('حساب النفقات') }}</th>
                                <th>{{ __('الفئة') }}</th>
                                <th>{{ __('الوحدة') }}</th>
                                <th>{{ __('الكمية') }}</th>
                                <th>{{ __('النوع') }}</th>
                                <th>{{ __('العمليات') }}</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach ($productServices as $productService)
                                <tr class="font-style" data-id="{{ $productService->id }}">
                                    <!-- اسم المنتج - قابل للتحرير -->
                                    <td class="editable" data-field="name">{{ $productService->name }}</td>

                                    <!-- الباركود - قابل للتحرير -->
                                    <td class="editable" data-field="sku">{{ $productService->sku }}</td>

                                    <!-- سعر البيع - قابل للتحرير -->
                                    <td class="editable" data-field="sale_price">{{ \Auth::user()->priceFormat($productService->sale_price) }}</td>

                                    <!-- سعر الشراء - قابل للتحرير -->
                                    <td class="editable" data-field="purchase_price">{{ \Auth::user()->priceFormat($productService->purchase_price) }}</td>

                                    <!-- حساب الدخل - قابل للتحرير -->
                                    <td class="editable-select" data-field="sale_chartaccount_id">
                                        @php
                                            $incomeAccount = '';
                                            if (!empty($productService->sale_chartaccount_id)) {
                                                $account = \App\Models\ChartOfAccount::find($productService->sale_chartaccount_id);
                                                $incomeAccount = $account ? $account->name : 'غير محدد';
                                            } else {
                                                $incomeAccount = 'غير محدد';
                                            }
                                        @endphp
                                        {{ $incomeAccount }}
                                    </td>

                                    <!-- حساب النفقات - قابل للتحرير -->
                                    <td class="editable-select" data-field="expense_chartaccount_id">
                                        @php
                                            $expenseAccount = '';
                                            if (!empty($productService->expense_chartaccount_id)) {
                                                $account = \App\Models\ChartOfAccount::find($productService->expense_chartaccount_id);
                                                $expenseAccount = $account ? $account->name : 'غير محدد';
                                            } else {
                                                $expenseAccount = 'غير محدد';
                                            }
                                        @endphp
                                        {{ $expenseAccount }}
                                    </td>

                                    <!-- الفئة - قابل للتحرير -->
                                    <td class="editable-select" data-field="category_id">{{ !empty($productService->category) ? $productService->category->name : 'غير محدد' }}</td>

                                    <!-- الوحدة - قابل للتحرير -->
                                    <td class="editable-select" data-field="unit_id">{{ !empty($productService->unit) ? $productService->unit->name : 'غير محدد' }}</td>

                                    <!-- الكمية - غير قابل للتحرير -->
                                    @if($productService->type == 'product')
                                        <td class="text-center">
                                            <span class="badge bg-info">{{ $productService->quantity }}</span>
                                        </td>
                                    @else
                                        <td class="text-center">
                                            <span class="badge bg-secondary">-</span>
                                        </td>
                                    @endif

                                    <!-- النوع - قابل للتحرير -->
                                    <td class="editable-select" data-field="type">
                                        @if($productService->type == 'product')
                                            <span class="badge bg-primary">{{ __('منتج') }}</span>
                                        @else
                                            <span class="badge bg-info">{{ __('خدمة') }}</span>
                                        @endif
                                    </td>

                                    <!-- العمليات -->
                                    <td class="Action">
                                        <!-- زر عرض التفاصيل -->
                                        <div class="action-btn">
                                            <a href="#" class="btn btn-sm align-items-center bg-warning" data-url="{{ route('productservice.detail',$productService->id) }}"
                                               data-ajax-popup="true" data-bs-toggle="tooltip" title="{{__('تفاصيل المستودع')}}" data-title="{{__('تفاصيل المستودع')}}">
                                                <i class="ti ti-eye text-white"></i>
                                            </a>
                                        </div>



                                        @can('delete product & service')
                                            <!-- زر الحذف -->
                                            <div class="action-btn">
                                                {!! Form::open(['method' => 'DELETE', 'route' => ['financial.products.destroy', $productService->id],'id'=>'delete-form-'.$productService->id, 'style' => 'display: inline;']) !!}
                                                <a href="#" class="btn btn-sm align-items-center bs-pass-para bg-danger" data-bs-toggle="tooltip" title="{{__('حذف')}}">
                                                    <i class="ti ti-trash text-white"></i>
                                                </a>
                                                {!! Form::close() !!}
                                            </div>
                                        @endcan
                                    </td>
                                </tr>
                            @endforeach

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>
@endsection

@push('style-page')
<style>
.editable, .editable-select {
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.3s ease;
    position: relative;
}

.editable:hover, .editable-select:hover {
    background-color: #f8f9fa;
    border: 1px dashed #007bff;
    box-shadow: 0 2px 4px rgba(0,123,255,0.1);
}

.editable:hover::after {
    content: "انقر للتعديل";
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    white-space: nowrap;
    z-index: 1000;
}

.editing {
    background-color: #fff3cd !important;
    border: 2px solid #ffc107 !important;
}

.table td {
    vertical-align: middle;
}

.action-btn {
    white-space: nowrap;
    display: inline-block;
}

.Action {
    text-align: center;
}

.Action .action-btn {
    margin: 2px;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
}

.btn-sm:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.badge {
    font-size: 0.75rem;
}

.table-responsive {
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.success-flash {
    background-color: #d4edda !important;
    border-color: #c3e6cb !important;
    animation: flashSuccess 2s ease-in-out;
}

.error-flash {
    background-color: #f8d7da !important;
    border-color: #f5c6cb !important;
    animation: flashError 2s ease-in-out;
}

@keyframes flashSuccess {
    0% { background-color: #d4edda; }
    50% { background-color: #c3e6cb; }
    100% { background-color: transparent; }
}

@keyframes flashError {
    0% { background-color: #f8d7da; }
    50% { background-color: #f5c6cb; }
    100% { background-color: transparent; }
}
</style>
@endpush

@push('script-page')
<script>
$(document).ready(function() {
    // Initialize DataTable
    if ($('#pc-dt-simple').length > 0) {
        try {
            $('#pc-dt-simple').DataTable({
                "pageLength": 25,
                "order": [[ 0, "asc" ]],
                "columnDefs": [
                    { "orderable": false, "targets": -1 }
                ],
                "language": {
                    "search": "البحث:",
                    "lengthMenu": "عرض _MENU_ عنصر",
                    "info": "عرض _START_ إلى _END_ من _TOTAL_ عنصر",
                    "paginate": {
                        "first": "الأول",
                        "last": "الأخير",
                        "next": "التالي",
                        "previous": "السابق"
                    }
                }
            });
        } catch (e) {
            console.log('DataTable initialization failed:', e);
        }
    }

    // Get dropdown options for select fields
    const categoryOptions = @json($category->toArray());
    const unitOptions = @json($units->toArray());
    const incomeAccountOptions = @json($incomeChartAccounts->toArray());
    const expenseAccountOptions = @json($expenseChartAccounts->toArray());
    const typeOptions = {
        'product': 'منتج',
        'service': 'خدمة'
    };

    // Handle inline editing for text fields
    $(document).on('click', '.editable', function() {
        var $this = $(this);

        // Prevent multiple edits
        if ($this.find('input').length > 0) {
            return;
        }

        var field = $this.data('field');
        var currentValue = $this.text().trim();
        var productId = $this.closest('tr').data('id');

        // Store original value
        $this.data('original-value', currentValue);
        $this.addClass('editing');

        // Remove currency formatting for price fields
        if (field === 'sale_price' || field === 'purchase_price') {
            currentValue = currentValue.replace(/[^\d.-]/g, '');
        }

        var inputType = 'text';
        var inputAttrs = {};

        // Set input type and attributes based on field
        if (field === 'sale_price' || field === 'purchase_price') {
            inputType = 'number';
            inputAttrs = {
                'step': '0.01',
                'min': '0'
            };
        }

        var input = $('<input type="' + inputType + '" class="form-control form-control-sm">');
        input.val(currentValue);

        // Set additional attributes
        $.each(inputAttrs, function(key, value) {
            input.attr(key, value);
        });

        $this.html(input);
        input.focus().select();

        // Handle save/cancel
        input.on('blur keypress', function(e) {
            if (e.type === 'keypress' && e.which !== 13) return;

            var newValue = $(this).val().trim();

            if (newValue !== currentValue && newValue !== '') {
                updateProduct(productId, field, newValue, $this);
            } else {
                restoreOriginalValue($this, field);
            }
        });

        // Cancel on Escape
        input.on('keydown', function(e) {
            if (e.which === 27) { // Escape key
                restoreOriginalValue($this, field);
            }
        });
    });

    // Handle inline editing for select fields
    $(document).on('click', '.editable-select', function() {
        var $this = $(this);

        // Prevent multiple edits
        if ($this.find('select').length > 0) {
            return;
        }

        var field = $this.data('field');
        var currentValue = $this.text().trim();
        var productId = $this.closest('tr').data('id');

        // Store original value
        $this.data('original-value', currentValue);
        $this.addClass('editing');

        var select = $('<select class="form-control form-control-sm">');
        var options = {};

        // Get appropriate options based on field
        switch(field) {
            case 'category_id':
                options = categoryOptions;
                break;
            case 'unit_id':
                options = unitOptions;
                break;
            case 'tax_id':
                options = taxOptions;
                break;
            case 'sale_chartaccount_id':
                options = incomeAccountOptions;
                break;
            case 'expense_chartaccount_id':
                options = expenseAccountOptions;
                break;
            case 'type':
                options = typeOptions;
                break;
        }

        // Add default option
        select.append('<option value="">اختر...</option>');

        // Add options
        $.each(options, function(value, text) {
            var selected = (text === currentValue || value === currentValue) ? 'selected' : '';
            select.append('<option value="' + value + '" ' + selected + '>' + text + '</option>');
        });

        $this.html(select);
        select.focus();

        // Handle save/cancel
        select.on('blur change', function(e) {
            var newValue = $(this).val();
            var newText = $(this).find('option:selected').text();

            if (newValue && newText !== currentValue) {
                updateProduct(productId, field, newValue, $this, newText);
            } else {
                restoreOriginalValue($this, field);
            }
        });

        // Cancel on Escape
        select.on('keydown', function(e) {
            if (e.which === 27) { // Escape key
                restoreOriginalValue($this, field);
            }
        });
    });

    // Update product function
    function updateProduct(id, field, value, element, displayText = null) {
        // Show loading state
        element.html('<i class="ti ti-loader fa-spin"></i>');
        element.removeClass('editing');

        $.ajax({
            url: '{{ route("financial.products.update", ":id") }}'.replace(':id', id),
            method: 'PUT',
            data: {
                _token: '{{ csrf_token() }}',
                field: field,
                value: value
            },
            success: function(response) {
                if (response.success || response.message) {
                    // Format display value for different field types
                    var displayValue = displayText || value;

                    if (field === 'sale_price' || field === 'purchase_price') {
                        displayValue = '{{ \Auth::user()->currencySymbol() }}' + parseFloat(value).toFixed(2);
                    } else if (field === 'type') {
                        if (value === 'product') {
                            displayValue = '<span class="badge bg-primary">منتج</span>';
                        } else {
                            displayValue = '<span class="badge bg-info">خدمة</span>';
                        }
                    }

                    element.html(displayValue);

                    // Add success animation
                    element.addClass('success-flash');
                    setTimeout(function() {
                        element.removeClass('success-flash');
                    }, 2000);

                    show_toastr('نجح', 'تم تحديث البيانات بنجاح', 'success');
                } else {
                    restoreOriginalValue(element, field);
                    show_toastr('خطأ', response.message || 'فشل في تحديث البيانات', 'error');
                }
            },
            error: function(xhr) {
                restoreOriginalValue(element, field);
                var errorMsg = 'فشل في تحديث البيانات';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                }
                show_toastr('خطأ', errorMsg, 'error');
            }
        });
    }

    // Helper function to restore original value
    function restoreOriginalValue(element, field) {
        var originalValue = element.data('original-value');

        if (field === 'type') {
            if (originalValue.includes('منتج')) {
                originalValue = '<span class="badge bg-primary">منتج</span>';
            } else {
                originalValue = '<span class="badge bg-info">خدمة</span>';
            }
        }

        element.html(originalValue);
        element.removeClass('editing');

        // Add error animation
        element.addClass('error-flash');
        setTimeout(function() {
            element.removeClass('error-flash');
        }, 2000);
    }

    // Handle edit button click (ajax popup)
    $(document).on('click', '[data-ajax-popup="true"]', function(e) {
        e.preventDefault();

        var url = $(this).data('url');
        var title = $(this).data('title') || 'تعديل';
        var size = $(this).data('size') || 'md';

        if (!url) {
            console.error('No URL provided for ajax popup');
            return;
        }

        // Show loading modal
        var modalHtml = `
            <div class="modal fade" id="commonModal" tabindex="-1" role="dialog" aria-labelledby="commonModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-${size}" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="commonModalLabel">${title}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="text-center">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">جاري التحميل...</span>
                                </div>
                                <p class="mt-2">جاري التحميل...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        $('#commonModal').remove();

        // Add modal to body
        $('body').append(modalHtml);

        // Show modal
        $('#commonModal').modal('show');

        // Load content via AJAX
        $.ajax({
            url: url,
            type: 'GET',
            success: function(response) {
                $('#commonModal .modal-body').html(response);

                // Initialize any form elements in the loaded content
                if (typeof initializeFormElements === 'function') {
                    initializeFormElements();
                }
            },
            error: function(xhr, status, error) {
                var errorMsg = 'حدث خطأ أثناء تحميل المحتوى';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                }

                $('#commonModal .modal-body').html(`
                    <div class="alert alert-danger">
                        <i class="ti ti-alert-circle me-2"></i>
                        ${errorMsg}
                    </div>
                `);
            }
        });
    });

    // Handle form submission in modal
    $(document).on('submit', '#commonModal form', function(e) {
        e.preventDefault();

        var form = $(this);
        var url = form.attr('action');
        var method = form.attr('method') || 'POST';
        var formData = new FormData(this);

        // Show loading state
        var submitBtn = form.find('input[type="submit"], button[type="submit"]');
        var originalText = submitBtn.val() || submitBtn.text();
        submitBtn.prop('disabled', true);

        if (submitBtn.is('input')) {
            submitBtn.val('جاري الحفظ...');
        } else {
            submitBtn.html('<i class="ti ti-loader fa-spin me-1"></i>جاري الحفظ...');
        }

        $.ajax({
            url: url,
            type: method,
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success || response.message) {
                    show_toastr('نجح', response.message || 'تم الحفظ بنجاح', 'success');
                    $('#commonModal').modal('hide');

                    // Reload page to show updated data
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    show_toastr('خطأ', response.message || 'فشل في الحفظ', 'error');
                }
            },
            error: function(xhr) {
                var errorMsg = 'فشل في الحفظ';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg = xhr.responseJSON.message;
                } else if (xhr.responseJSON && xhr.responseJSON.errors) {
                    var errors = Object.values(xhr.responseJSON.errors).flat();
                    errorMsg = errors.join(', ');
                }
                show_toastr('خطأ', errorMsg, 'error');
            },
            complete: function() {
                // Restore button state
                submitBtn.prop('disabled', false);
                if (submitBtn.is('input')) {
                    submitBtn.val(originalText);
                } else {
                    submitBtn.html(originalText);
                }
            }
        });
    });

    // Clean up modal when hidden
    $(document).on('hidden.bs.modal', '#commonModal', function() {
        $(this).remove();
    });

    // Handle delete button click
    $(document).on('click', '.bs-pass-para', function(e) {
        e.preventDefault();

        var form = $(this).closest('form');
        var url = form.attr('action');

        // Show confirmation dialog
        if (confirm('هل أنت متأكد من حذف هذا المنتج؟ لا يمكن التراجع عن هذا الإجراء.')) {
            // Show loading state
            $(this).html('<i class="ti ti-loader fa-spin"></i>').prop('disabled', true);

            $.ajax({
                url: url,
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success || response.message) {
                        show_toastr('نجح', response.message || 'تم حذف المنتج بنجاح', 'success');

                        // Remove row from table
                        form.closest('tr').fadeOut(function() {
                            $(this).remove();
                        });
                    } else {
                        show_toastr('خطأ', response.message || 'فشل في حذف المنتج', 'error');
                    }
                },
                error: function(xhr) {
                    var errorMsg = 'فشل في حذف المنتج';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg = xhr.responseJSON.message;
                    }
                    show_toastr('خطأ', errorMsg, 'error');
                },
                complete: function() {
                    // Restore button state
                    form.find('.bs-pass-para').html('<i class="ti ti-trash text-white"></i>').prop('disabled', false);
                }
            });
        }
    });
});
</script>
@endpush
