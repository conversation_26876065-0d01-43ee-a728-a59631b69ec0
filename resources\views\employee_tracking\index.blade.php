@extends('layouts.admin')

@section('page-title')
    {{ __('Employee Tracking') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('Employee Tracking') }}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        <a href="{{ route('employee.tracking.live.map') }}" class="btn btn-sm btn-primary">
            <i class="ti ti-map"></i> {{ __('Live Map') }}
        </a>
        <a href="{{ route('employee.tracking.attendance.report') }}" class="btn btn-sm btn-info">
            <i class="ti ti-file-report"></i> {{ __('Attendance Report') }}
        </a>
    </div>
@endsection

@section('content')
    <div class="row">
        <!-- Filters -->
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ route('employee.tracking.index') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="branch_id">{{ __('Branch') }}</label>
                                    <select name="branch_id" id="branch_id" class="form-control">
                                        <option value="">{{ __('All Branches') }}</option>
                                        @foreach($branches as $branch)
                                            <option value="{{ $branch->id }}" {{ $selectedBranch == $branch->id ? 'selected' : '' }}>
                                                {{ $branch->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="department_id">{{ __('Department') }}</label>
                                    <select name="department_id" id="department_id" class="form-control">
                                        <option value="">{{ __('All Departments') }}</option>
                                        @foreach($departments as $department)
                                            <option value="{{ $department->id }}" {{ $selectedDepartment == $department->id ? 'selected' : '' }}>
                                                {{ $department->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="date">{{ __('Date') }}</label>
                                    <input type="date" name="date" id="date" class="form-control" value="{{ $selectedDate }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="ti ti-filter"></i> {{ __('Filter') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Employee Tracking Data -->
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('Employee Tracking for') }} {{ $selectedDate }}</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>{{ __('Employee') }}</th>
                                    <th>{{ __('Branch') }}</th>
                                    <th>{{ __('Department') }}</th>
                                    <th>{{ __('Clock In') }}</th>
                                    <th>{{ __('Clock Out') }}</th>
                                    <th>{{ __('Status') }}</th>
                                    <th>{{ __('Locations') }}</th>
                                    <th>{{ __('Actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($attendanceData as $data)
                                    @php
                                        $employee = $data['employee'];
                                        $attendance = $data['attendance'];
                                        $locations = $data['locations'];
                                    @endphp
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar avatar-sm me-2">
                                                    <span class="avatar-title bg-primary rounded-circle">
                                                        {{ substr($employee->name, 0, 1) }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <strong>{{ $employee->name }}</strong><br>
                                                    <small class="text-muted">{{ $employee->email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ $employee->branch->name ?? 'N/A' }}</td>
                                        <td>{{ $employee->department->name ?? 'N/A' }}</td>
                                        <td>
                                            @if($attendance && $attendance->clock_in != '00:00:00')
                                                <span class="badge bg-success">{{ $attendance->clock_in }}</span>
                                                @if($attendance->late != '00:00:00')
                                                    <br><small class="text-danger">Late: {{ $attendance->late }}</small>
                                                @endif
                                            @else
                                                <span class="badge bg-secondary">{{ __('Not Clocked In') }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($attendance && $attendance->clock_out != '00:00:00')
                                                <span class="badge bg-info">{{ $attendance->clock_out }}</span>
                                                @if($attendance->overtime != '00:00:00')
                                                    <br><small class="text-primary">Overtime: {{ $attendance->overtime }}</small>
                                                @endif
                                            @else
                                                <span class="badge bg-warning">{{ __('Not Clocked Out') }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($attendance)
                                                <span class="badge bg-{{ $attendance->status == 'Present' ? 'success' : 'danger' }}">
                                                    {{ $attendance->status }}
                                                </span>
                                            @else
                                                <span class="badge bg-secondary">{{ __('Absent') }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ $locations->count() }} {{ __('Locations') }}</span>
                                        </td>
                                        <td>
                                            <a href="{{ route('employee.tracking.show', $employee->id) }}?date={{ $selectedDate }}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="ti ti-eye"></i> {{ __('View Details') }}
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center">{{ __('No employees found') }}</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<script>
    // Auto-refresh every 5 minutes
    setTimeout(function() {
        location.reload();
    }, 300000);
</script>
@endpush
