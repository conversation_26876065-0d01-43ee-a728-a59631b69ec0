@extends('layouts.admin')

@section('page-title')
    {{ __('Live Employee Map') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ route('employee.tracking.index') }}">{{ __('Employee Tracking') }}</a></li>
    <li class="breadcrumb-item">{{ __('Live Map') }}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        <button id="refreshMap" class="btn btn-sm btn-primary">
            <i class="ti ti-refresh"></i> {{ __('Refresh') }}
        </button>
        <div class="btn-group">
            <button type="button" class="btn btn-sm btn-info dropdown-toggle" data-bs-toggle="dropdown">
                <i class="ti ti-filter"></i> {{ __('Filter') }}
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="filterEmployees('all')">{{ __('All Employees') }}</a></li>
                <li><a class="dropdown-item" href="#" onclick="filterEmployees('online')">{{ __('Online Only') }}</a></li>
                <li><a class="dropdown-item" href="#" onclick="filterEmployees('offline')">{{ __('Offline Only') }}</a></li>
            </ul>
        </div>
    </div>
@endsection

@section('content')
    <div class="row">
        <!-- Employee List -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('Employees') }} (<span id="employeeCount">{{ count($liveLocations) }}</span>)</h5>
                </div>
                <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                    <div id="employeeList">
                        @foreach($liveLocations as $location)
                            @php
                                $employee = $location['employee'];
                                $loc = $location['location'];
                                $isOnline = $location['is_online'];
                            @endphp
                            <div class="employee-item mb-3 p-3 border rounded" data-status="{{ $isOnline ? 'online' : 'offline' }}" 
                                 data-employee-id="{{ $employee->id }}" 
                                 data-lat="{{ $loc['latitude'] }}" 
                                 data-lng="{{ $loc['longitude'] }}">
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm me-2">
                                        <span class="avatar-title bg-{{ $isOnline ? 'success' : 'secondary' }} rounded-circle">
                                            {{ substr($employee->name, 0, 1) }}
                                        </span>
                                    </div>
                                    <div class="flex-grow-1">
                                        <strong>{{ $employee->name }}</strong>
                                        <br>
                                        <small class="text-muted">{{ $employee->branch->name ?? 'N/A' }}</small>
                                        <br>
                                        <span class="badge bg-{{ $isOnline ? 'success' : 'secondary' }}">
                                            {{ $isOnline ? __('Online') : __('Offline') }}
                                        </span>
                                    </div>
                                    <button class="btn btn-sm btn-outline-primary" onclick="focusOnEmployee({{ $employee->id }})">
                                        <i class="ti ti-map-pin"></i>
                                    </button>
                                </div>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="ti ti-clock"></i> 
                                        {{ \Carbon\Carbon::parse($loc['timestamp'])->diffForHumans() }}
                                    </small>
                                    @if($loc['address'])
                                        <br>
                                        <small class="text-muted">
                                            <i class="ti ti-map-pin"></i> 
                                            {{ Str::limit($loc['address'], 50) }}
                                        </small>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

        <!-- Map -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5>{{ __('Live Location Map') }}</h5>
                </div>
                <div class="card-body">
                    <div id="map" style="height: 600px; width: 100%;"></div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<script src="https://maps.googleapis.com/maps/api/js?key=YOUR_GOOGLE_MAPS_API_KEY&callback=initMap" async defer></script>
<script>
let map;
let markers = [];
let employeeLocations = @json($liveLocations);

function initMap() {
    // Initialize map centered on first employee or default location
    let center = {lat: 24.7136, lng: 46.6753}; // Riyadh default
    
    if (employeeLocations.length > 0 && employeeLocations[0].location.latitude) {
        center = {
            lat: parseFloat(employeeLocations[0].location.latitude),
            lng: parseFloat(employeeLocations[0].location.longitude)
        };
    }

    map = new google.maps.Map(document.getElementById('map'), {
        zoom: 12,
        center: center,
        mapTypeId: 'roadmap'
    });

    // Add markers for all employees
    addEmployeeMarkers();
}

function addEmployeeMarkers() {
    // Clear existing markers
    markers.forEach(marker => marker.setMap(null));
    markers = [];

    employeeLocations.forEach(location => {
        const employee = location.employee;
        const loc = location.location;
        const isOnline = location.is_online;

        if (loc.latitude && loc.longitude) {
            const marker = new google.maps.Marker({
                position: {
                    lat: parseFloat(loc.latitude),
                    lng: parseFloat(loc.longitude)
                },
                map: map,
                title: employee.name,
                icon: {
                    url: isOnline ? 
                        'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="green"><circle cx="12" cy="12" r="10"/></svg>') :
                        'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent('<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="gray"><circle cx="12" cy="12" r="10"/></svg>'),
                    scaledSize: new google.maps.Size(24, 24)
                }
            });

            const infoWindow = new google.maps.InfoWindow({
                content: `
                    <div>
                        <h6>${employee.name}</h6>
                        <p><strong>Branch:</strong> ${employee.branch ? employee.branch.name : 'N/A'}</p>
                        <p><strong>Status:</strong> <span class="badge bg-${isOnline ? 'success' : 'secondary'}">${isOnline ? 'Online' : 'Offline'}</span></p>
                        <p><strong>Last Update:</strong> ${new Date(loc.timestamp).toLocaleString()}</p>
                        ${loc.address ? `<p><strong>Address:</strong> ${loc.address}</p>` : ''}
                        <a href="/employee-tracking/employee/${employee.id}" class="btn btn-sm btn-primary">View Details</a>
                    </div>
                `
            });

            marker.addListener('click', () => {
                infoWindow.open(map, marker);
            });

            markers.push({
                marker: marker,
                employee: employee,
                isOnline: isOnline
            });
        }
    });
}

function focusOnEmployee(employeeId) {
    const markerData = markers.find(m => m.employee.id === employeeId);
    if (markerData) {
        map.setCenter(markerData.marker.getPosition());
        map.setZoom(15);
        
        // Trigger click to show info window
        google.maps.event.trigger(markerData.marker, 'click');
    }
}

function filterEmployees(status) {
    const employeeItems = document.querySelectorAll('.employee-item');
    let visibleCount = 0;

    employeeItems.forEach(item => {
        const itemStatus = item.getAttribute('data-status');
        
        if (status === 'all' || itemStatus === status) {
            item.style.display = 'block';
            visibleCount++;
        } else {
            item.style.display = 'none';
        }
    });

    // Update markers visibility
    markers.forEach(markerData => {
        const shouldShow = status === 'all' || 
                          (status === 'online' && markerData.isOnline) || 
                          (status === 'offline' && !markerData.isOnline);
        
        markerData.marker.setVisible(shouldShow);
    });

    document.getElementById('employeeCount').textContent = visibleCount;
}

function refreshMap() {
    location.reload();
}

// Auto-refresh every 2 minutes
setInterval(refreshMap, 120000);

// Event listeners
document.getElementById('refreshMap').addEventListener('click', refreshMap);
</script>
@endpush
