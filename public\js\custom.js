/**
 *
 * You can write your JS code here, DO NOT touch the default style file
 * because it will make it harder for you to update.
 *
 */

"use strict";
// for pos system
var session_key = $(location).attr("href").split('/').pop();
//

$(function () {
    if ($('.custom-scroll').length) {
        $(".custom-scroll").niceScroll();
        $(".custom-scroll-horizontal").niceScroll();
    }


    // loadConfirm();


});

//file validation
$(document).ready(function() {
    $(document).on("change",".file-validate",function(){
        let file_input = $(this)[0];
        let max_size = file_size;
        let allowed_extensions = file_types;
        let file_error = $('.file-error');
        file_error.text('');
        if (file_input.files.length > 0) {
            let file = file_input.files[0];
            let file_size = file.size / 1024;
            let file_extension = file.name.split('.').pop().toLowerCase();
            let extensions_array = allowed_extensions.split(',');
            if (!extensions_array.includes(file_extension)) {
                $(this).next().text(type_err);
                file_input.value = '';
                // document.getElementById('blah').src = '';
                return false;
            } else if (file_size > max_size) {
                $(this).next().text(size_err);
                file_input.value = '';
                // document.getElementById('blah').src = '';
                return false;
            }
        }
        return true;
    });
});


function validation() {
    var forms = document.querySelectorAll('.needs-validation');

    Array.prototype.forEach.call(forms, function (form) {

        form.addEventListener('submit', function (event) {
            var submitButton = form.querySelector('button[type="submit"], input[type="submit"]');

            if (submitButton) {
                submitButton.disabled = true;
            }
            if (form.checkValidity() === false) {
                event.preventDefault();
                event.stopPropagation();
                if (submitButton) {
                    submitButton.disabled = false;
                }
            }

            form.classList.add('was-validated');
        }, false);
    });
}

// Import Data for Dynamic function
function SetDynamicData(tbody, thead, count = 0) {
    if (count < 8) {
        var process_area = document.getElementById("process_area");
        if (process_area) {
            var theadHtml = '<tr>';
            for (let i = 0; i < Object.values(thead).length; i++) {
                theadHtml += '<th>';
                theadHtml += '<select name="set_column_data" class="form-control set_column_data" data-column_number="' + i + '">';
                theadHtml += '<option value="">Select Field</option>';
                $.each(thead, function(key, value) {
                    let formattedText = value.replace(/_/g, ' ').replace(/\b\w/g, function(letter) {
                        return letter.toUpperCase();
                    });
                    theadHtml += '<option value="' + value + '">' + formattedText + '</option>';
                });
                theadHtml += '</select>';
                theadHtml += '</th>';
            }
            theadHtml += '</tr>';

            $('#process_area .table .thead').html(theadHtml);
            $('#process_area .table .tbody').html(tbody);
        }
        else {
            setTimeout(function () {
                SetDynamicData(tbody, count + 1);
            }, 500);
        }
    }
    else {
        show_toastr('Success', '{{ __("Something went wrong, please try again!") }}', 'success');
    }
}

// Import Data
function SetData(params, count = 0) {
    if (count < 8) {
        var process_area = document.getElementById("process_area");
        if (process_area) {
            $('#process_area').html(params);
        }
        else {
            setTimeout(function () {
                SetData(params, count + 1);
            }, 500);
        }
    }
    else {
        toastrs('Success', '{{ __("Something went wrong, please try again!") }}', 'success');
    }
}

$(document).ready(function () {
    if ($(".datatable").length > 0) {
        const dataTable =  new simpleDatatables.DataTable(".datatable");

    }

    select2();
    summernote();
    daterange();
    // loadConfirm();
});

$(document).ready(function () {

    if ($(".needs-validation").length > 0) {
        validation();
    }

    if ($(".pc-dt-simple").length > 0) {
        $( $(".pc-dt-simple") ).each(function( index,element ) {
            var id = $(element).attr('id');
            const dataTable = new simpleDatatables.DataTable("#"+id);
        });
    }

    select2();
    summernote();
    daterange();
});

function daterange() {
    if ($("#pc-daterangepicker-1").length > 0) {
        document.querySelector("#pc-daterangepicker-1").flatpickr({
            mode: "range"
        });
    }
}

function select2() {
    $(".select2").each(function () {
        if (!$(this).data('initialized')) {
            var id = $(this).attr('id');
            if (id) {
                new Choices('#' + id, {
                    removeItemButton: true,
                });
                $(this).data('initialized', true);
            }
        }
    });
}


function show_toastr(type, message) {

    var f = document.getElementById('liveToast');
    var a = new bootstrap.Toast(f).show();
    if (type == 'success') {
        $('#liveToast').addClass('bg-primary');
    } else {
        $('#liveToast').addClass('bg-danger');
    }
    $('#liveToast .toast-body').html(message);
}

$(document).on('click', 'a[data-ajax-popup="true"], button[data-ajax-popup="true"], div[data-ajax-popup="true"]', function (e) {
    console.log('AJAX popup clicked:', this);

    var data = {};
    var title1 = $(this).data("title");
    var id = $(this).attr("id");
    var title2 = $(this).data("bs-original-title");
    var title3 = $(this).data("original-title");
    var title = (title1 != undefined) ? title1 : title2;
    var title=(title != undefined) ? title : title3;

    console.log('Element ID:', id);
    console.log('Element title:', title);

    $('#commonModal .modal-dialog').removeClass('modal-sm modal-md modal-lg modal-xl modal-xxl');
    var size = ($(this).data('size') == '') ? 'md' : $(this).data('size');

    if(id =="payment")
    {
        // التحقق من اختيار العميل والمستودع قبل الدفع
        var customerId = $('#vc_name_hidden').val();
        var warehouseId = $('#warehouse_name_hidden').val();

        if (!customerId || customerId == '0' || customerId == '') {
            show_toastr('error', 'يرجى اختيار عميل أولاً!', 'error');
            return false;
        }

        if (!warehouseId || warehouseId == '0' || warehouseId == '') {
            show_toastr('error', 'يرجى اختيار مستودع أولاً!', 'error');
            return false;
        }

        var url = $('#payment').data('url');
        // التحقق من وجود URL صحيح
        if (!url) {
            show_toastr('error', 'Payment URL not found', 'error');
            return false;
        }

        console.log('Payment URL:', url);
        console.log('Customer ID:', customerId);
        console.log('Warehouse ID:', warehouseId);
    }else{
        var url = $(this).data('url');
    }

    $("#commonModal .modal-title").html(title);
    $("#commonModal .modal-dialog").addClass('modal-' + size);

    if ($('#vc_name_hidden').length > 0) {
        data['vc_name'] = $('#vc_name_hidden').val();
    }
    if ($('#delivery_user_hidden').length > 0) {
        data['user_id'] = $('#delivery_user_hidden').val();
    }
    if ($('#warehouse_name_hidden').length > 0) {
        data['warehouse_name'] = $('#warehouse_name_hidden').val();
    }
    if ($('#discount_hidden').length > 0) {
        data['discount'] = $('#discount_hidden').val();
    }
    if ($('#quotation_id').length > 0) {
        data['quotation_id'] = $('#quotation_id').val();
    }
    $.ajax({
        url: url,
        data: data,
        beforeSend: function() {
            // إظهار مؤشر التحميل
            if(id == "payment") {
                $('#payment').prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status"></span> جاري التحميل...');
            }
        },
        success: function (data) {
            $('#commonModal .modal-body').html(data);

            // إصلاح aria-hidden قبل فتح المودال
            $("#commonModal").removeAttr('aria-hidden');
            $("#commonModal").modal('show');

            taskCheckbox();
            common_bind("#commonModal");
            validation();
            commonLoader();

            // إعادة تفعيل زر الدفع
            if(id == "payment") {
                $('#payment').prop('disabled', false).html('<i class="fas fa-credit-card me-2"></i>دفع');
            }
        },
        error: function (xhr, status, error) {
            console.error('AJAX Error:', xhr.responseText);

            var errorMessage = 'حدث خطأ أثناء تحميل صفحة الدفع';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
            } else if (xhr.status === 404) {
                errorMessage = 'صفحة الدفع غير موجودة';
            } else if (xhr.status === 500) {
                errorMessage = 'خطأ في الخادم';
            }

            show_toastr('Error', errorMessage, 'error');

            // إعادة تفعيل زر الدفع
            if(id == "payment") {
                $('#payment').prop('disabled', false).html('<i class="fas fa-credit-card me-2"></i>دفع');
            }
        }
    });

});


function arrayToJson(form) {
    var data = $(form).serializeArray();
    var indexed_array = {};

    $.map(data, function (n, i) {
        indexed_array[n['name']] = n['value'];
    });

    return indexed_array;
}


function common_bind() {
    select2();
}


function taskCheckbox() {
    var checked = 0;
    var count = 0;
    var percentage = 0;

    count = $("#check-list input[type=checkbox]").length;
    checked = $("#check-list input[type=checkbox]:checked").length;
    percentage = parseInt(((checked / count) * 100), 10);
    if (isNaN(percentage)) {
        percentage = 0;
    }
    $(".custom-label").text(percentage + "%");
    $('#taskProgress').css('width', percentage + '%');


    $('#taskProgress').removeClass('bg-warning');
    $('#taskProgress').removeClass('bg-primary');
    $('#taskProgress').removeClass('bg-success');
    $('#taskProgress').removeClass('bg-danger');

    if (percentage <= 15) {
        $('#taskProgress').addClass('bg-danger');
    } else if (percentage > 15 && percentage <= 33) {
        $('#taskProgress').addClass('bg-warning');
    } else if (percentage > 33 && percentage <= 70) {
        $('#taskProgress').addClass('bg-primary');
    } else {
        $('#taskProgress').addClass('bg-success');
    }
}


function commonLoader() {
    $('[data-toggle="tooltip"]').tooltip();
    if ($('[data-toggle="tags"]').length > 0) {
        $('[data-toggle="tags"]').tagsinput({tagClass: "badge badge-primary"});
    }


    // $(function(){
    //
    //     var dtToday = new Date();
    //
    //     var month = dtToday.getMonth() + 1;
    //     var day = dtToday.getDate();
    //     var year = dtToday.getFullYear();
    //     if(month < 10)
    //         month = '0' + month.toString();
    //     if(day < 10)
    //         day = '0' + day.toString();
    //
    //     var maxDate = year + '-' + month + '-' + day;
    //
    //     $("input[type='date']").attr('max', maxDate);
    // });

    var e = $(".scrollbar-inner");
    e.length && e.scrollbar().scrollLock()

    var e1 = $(".custom-input-file");
    e1.length && e1.each(function () {
        var e1 = $(this);
        e1.on("change", function (t) {
            !function (e, t, a) {
                var n, o = e.next("label"), i = o.html();
                t && t.files.length > 1 ? n = (t.getAttribute("data-multiple-caption") || "").replace("{count}", t.files.length) : a.target.value && (n = a.target.value.split("\\").pop()), n ? o.find("span").html(n) : o.html(i)
            }(e1, this, t)
        }), e1.on("focus", function () {
            !function (e) {
                e.addClass("has-focus")
            }(e1)
        }).on("blur", function () {
            !function (e) {
                e.removeClass("has-focus")
            }(e1)
        })
    })

    // var e2 = $('[data-toggle="autosize"]');
    // e2.length && autosize(e2);


    if ($(".jscolor").length) {
        jscolor.installByClassName("jscolor");
    }
    summernote();
    // for Choose file
    $(document).on('change', 'input[type=file]', function () {
        var fileclass = $(this).attr('data-filename');
        var finalname = $(this).val().split('\\').pop();
        $('.' + fileclass).html(finalname);
    });
}


function summernote() {
    if ($(".summernote-simple").length) {
        $('.summernote-simple').summernote({
            placeholder: "Write Here… ",
            tabsize: 2,
            minHeight: 200,
            maxHeight: 250,
            toolbar: [
                ['style', ['style']],
                ['font', ['bold', 'italic', 'underline', 'strikethrough']],
                ['list', ['ul', 'ol', 'paragraph']],
                ['insert', ['link', 'unlink']],
            ],
            height: 250,
});
        $('.dropdown-toggle').dropdown();
    }

    if ($(".summernote-simple-2").length) {
        $('.summernote-simple-2').summernote({
            placeholder: "Write Here… ",
            dialogsInBody: !0,
            minHeight: 200,
            maxHeight: 250,
            toolbar: [
                ['style', ['style']],
                ["font", ["bold", "italic", "underline", "clear", "strikethrough"]],
                ['fontname', ['fontname']],
                ['color', ['color']],
                ["para", ["ul", "ol", "paragraph"]],
            ],

        });
    }

}



$(document).on("click", '.bs-pass-para', function () {
    var form = $(this).closest("form");
    const swalWithBootstrapButtons = Swal.mixin({
        customClass: {
            confirmButton: 'btn btn-success',
            cancelButton: 'btn btn-danger'
        },
        buttonsStyling: false
    })
    swalWithBootstrapButtons.fire({
        title: 'Are you sure?',
        text: "This action can not be undone. Do you want to continue?",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Yes',
        cancelButtonText: 'No',
        reverseButtons: true
    }).then((result) => {
        if (result.isConfirmed) {

            form.submit();

        } else if (
            result.dismiss === Swal.DismissReason.cancel
        ) {
        }
    })
});

//only pos system delete button - AJAX version to prevent page reload
// Note: This handler is for general pages. POS page has its own specialized handler.
$(document).on("click", '.bs-pass-para-pos', function (e) {
    // التحقق من أن هذا ليس في صفحة POS (التي لها معالج خاص)
    if (window.location.pathname.includes('/pos') || $('body').hasClass('pos-page')) {
        console.log('POS page detected - using specialized POS handler');
        return; // دع معالج POS يتولى الأمر
    }

    e.preventDefault(); // منع السلوك الافتراضي للرابط

    var formId = $(this).data('confirm-yes');
    var form = document.getElementById(formId);
    var button = $(this);

    // التحقق من وجود النموذج
    if (!form) {
        console.error('Form not found with ID: ' + formId);
        if (typeof show_toastr === 'function') {
            show_toastr('error', 'خطأ: لم يتم العثور على النموذج', 'error');
        }
        return;
    }

    // التحقق من أن النموذج يحتوي على معرف المنتج وليس فقط session_key
    var productIdInput = form.querySelector('input[name="id"]');
    var sessionKeyInput = form.querySelector('input[name="session_key"]');

    if (!productIdInput || !productIdInput.value) {
        console.error('Product ID not found in form');
        if (typeof show_toastr === 'function') {
            show_toastr('error', 'خطأ: معرف المنتج غير موجود', 'error');
        }
        return;
    }

    var productId = productIdInput.value;
    var sessionKey = sessionKeyInput ? sessionKeyInput.value : '';

    console.log('General page - Attempting to delete product with ID:', productId);

    // استخدام SweetAlert إذا كان متوفر
    if (typeof Swal !== 'undefined') {
        const swalWithBootstrapButtons = Swal.mixin({
            customClass: {
                confirmButton: 'btn btn-success',
                cancelButton: 'btn btn-danger'
            },
            buttonsStyling: false
        });

        swalWithBootstrapButtons.fire({
            title: 'هل أنت متأكد؟',
            text: "سيتم حذف هذا المنتج. لا يمكن التراجع عن هذا الإجراء!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'نعم، احذف',
            cancelButtonText: 'إلغاء',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                performGeneralDeletion(productId, sessionKey, button, form);
            }
        });
    } else {
        // استخدام confirm عادي إذا لم يكن SweetAlert متوفر
        if (confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
            performGeneralDeletion(productId, sessionKey, button, form);
        }
    }
});

// دالة تنفيذ الحذف للصفحات العامة
function performGeneralDeletion(productId, sessionKey, button, form) {
    // تعطيل الزر مؤقتاً لمنع النقر المتكرر
    var originalHtml = button.html();
    button.prop('disabled', true).html('<i class="ti ti-loader"></i>');

    // إرسال طلب AJAX لحذف المنتج
    $.ajax({
        url: '/remove-from-cart',
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
            'X-Requested-With': 'XMLHttpRequest'
        },
        data: {
            id: productId,
            session_key: sessionKey
        },
        success: function(response) {
            console.log('Product removed successfully:', response);

            if (response.code === 200) {
                // إزالة العنصر من الواجهة
                var productElement = $('#product-id-' + productId);
                if (productElement.length > 0) {
                    productElement.fadeOut(300, function() {
                        $(this).remove();
                    });
                } else {
                    // إذا لم يوجد عنصر محدد، إعادة تحميل الصفحة
                    location.reload();
                }

                // عرض رسالة نجاح
                if (typeof show_toastr === 'function') {
                    show_toastr('success', response.success || 'تم الحذف بنجاح!', 'success');
                }
            } else {
                // إعادة تفعيل الزر في حالة الخطأ
                button.prop('disabled', false).html(originalHtml);
                if (typeof show_toastr === 'function') {
                    show_toastr('error', response.error || 'فشل في الحذف', 'error');
                }
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', xhr.responseText);

            // إعادة تفعيل الزر في حالة الخطأ
            button.prop('disabled', false).html(originalHtml);

            var errorMessage = 'حدث خطأ أثناء الحذف';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
            }

            if (typeof show_toastr === 'function') {
                show_toastr('error', errorMessage, 'error');
            } else {
                alert(errorMessage);
            }
        }
    });
}

// دالة مساعدة لإعادة حساب المجاميع بعد حذف منتج (تستخدم نفس منطق الصفحة الأصلية)
function updateCartTotalsAfterRemoval() {
    // التحقق من وجود دالة updateCartTotalsAfterRemoval في الصفحة الأصلية
    if (typeof window.updateCartTotalsAfterRemoval === 'function') {
        window.updateCartTotalsAfterRemoval();
        return;
    }

    var subtotal = 0;
    var totalTax = 0;

    // حساب المجاميع من المنتجات المتبقية في الجدول
    $('#tbody tr:not(.no-found)').each(function() {
        var row = $(this);
        var quantity = parseFloat(row.find('input[name="quantity"]').val()) || 0;
        var priceText = row.find('.price').text().replace(/[^\d.-]/g, '');
        var price = parseFloat(priceText) || 0;
        var subtotalText = row.find('.subtotal').text().replace(/[^\d.-]/g, '');
        var itemSubtotal = parseFloat(subtotalText) || 0;

        subtotal += itemSubtotal;

        // حساب الضريبة (افتراض أن الضريبة مُضمنة في السعر)
        // يمكن تحسين هذا الحساب حسب نظام الضرائب المستخدم
        var taxRate = 15; // افتراض ضريبة 15%
        var itemTax = (itemSubtotal * taxRate) / (100 + taxRate);
        totalTax += itemTax;
    });

    // تحديث المجاميع في الواجهة
    if (typeof addCommas === 'function') {
        $('.subtotalamount').text(addCommas(subtotal.toFixed(2)));
        $('.taxamount').text(addCommas(totalTax.toFixed(2)));
        $('.totalamount').text(addCommas(subtotal.toFixed(2))); // المجموع = المجموع الفرعي (شامل الضريبة)
        $('#displaytotal').text(addCommas(subtotal.toFixed(2)));
    } else {
        // استخدام تنسيق بسيط إذا لم تكن دالة addCommas متوفرة
        $('.subtotalamount').text(subtotal.toFixed(2));
        $('.taxamount').text(totalTax.toFixed(2));
        $('.totalamount').text(subtotal.toFixed(2));
        $('#displaytotal').text(subtotal.toFixed(2));
    }

    // إذا لم تعد هناك منتجات، تصفير المجاميع
    if (subtotal === 0) {
        $('.subtotalamount').text('0.00');
        $('.taxamount').text('0.00');
        $('.totalamount').text('0.00');
        $('#displaytotal').text('0.00');
    }
}

// دالة مساعدة لتنسيق الأرقام (احتياطية)
function addCommas(num) {
    if (typeof num !== 'number') {
        num = parseFloat(num) || 0;
    }
    return num.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

// دالة مساعدة لعرض الرسائل (احتياطية)
function show_toastr(type, message, icon) {
    // التحقق من وجود دالة show_toastr في الصفحة الأصلية
    if (typeof window.show_toastr === 'function') {
        window.show_toastr(type, message, icon);
        return;
    }

    // استخدام alert كبديل إذا لم تكن دالة show_toastr متوفرة
    console.log(type + ': ' + message);
    if (type === 'error') {
        alert('خطأ: ' + message);
    } else if (type === 'success') {
        alert('نجح: ' + message);
    } else {
        alert(message);
    }
}


function postAjax(url, data, cb) {
    var token = $('meta[name="csrf-token"]').attr('content');
    var jdata = {_token: token};

    for (var k in data) {
        jdata[k] = data[k];
    }

    $.ajax({
        type: 'POST',
        url: url,
        data: jdata,
        success: function (data) {
            if (typeof (data) === 'object') {
                cb(data);
            } else {
                cb(data);
            }
        },
    });
}

//end only pos system delete button


function deleteAjax(url, data, cb) {
    var token = $('meta[name="csrf-token"]').attr('content');
    var jdata = {_token: token};

    for (var k in data) {
        jdata[k] = data[k];
    }

    $.ajax({
        type: 'DELETE',
        url: url,
        data: jdata,
        success: function (data) {
            if (typeof (data) === 'object') {
                cb(data);
            } else {
                cb(data);
            }
        },
    });
}

// Google calendar
$(document).on('click', '.local_calender .fc-daygrid-event, .fc-timegrid-event', function (e) {
    // if (!$(this).hasClass('project')) {
    e.preventDefault();
    var event = $(this);
    var title1 = $('.fc-event-title').html();
    var title2 = $(this).data("bs-original-title");
    var title = (title1 != undefined) ? title1 : title2;
    // var size = ($(this).data('size') == '') ? 'md' : $(this).data('size');
    var size = 'md';
    var url = $(this).attr('href');
    $("#commonModal .modal-title").html(title);
    $('#commonModal .modal-dialog').removeClass('modal-sm modal-md modal-lg modal-xl modal-xxl');
    $("#commonModal .modal-dialog").addClass('modal-' + size);
    $.ajax({
        url: url,
        success: function (data) {
            $('#commonModal .modal-body').html(data);

            // إصلاح aria-hidden قبل فتح المودال
            $("#commonModal").removeAttr('aria-hidden');
            $("#commonModal").modal('show');

            common_bind();
        },
        error: function (data) {
            data = data.responseJSON;
            toastrs('Error', data.error, 'error')
        }
    });
    // }
});

//date value 4

// $(function(){
//
//     var dtToday = new Date();
//
//     var month = dtToday.getMonth() + 1;
//     var day = dtToday.getDate();
//     var year = dtToday.getFullYear();
//     if(month < 10)
//         month = '0' + month.toString();
//     if(day < 10)
//         day = '0' + day.toString();
//
//     var maxDate = year + '-' + month + '-' + day;
//
//     $("input[type='date']").attr('max', maxDate);
// });

function addCommas(num) {
    var number = parseFloat(num).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,');
    return ((site_currency_symbol_position == "pre") ? site_currency_symbol : '') + number + ((site_currency_symbol_position == "post") ? site_currency_symbol : '');
}



// PLUS MINUS QUANTITY JS
function wcqib_refresh_quantity_increments() {
    jQuery("div.quantity:not(.buttons_added), td.quantity:not(.buttons_added)").each(function (a, b) {
        var c = jQuery(b);
        c.addClass("buttons_added"),
            c.children().first().before('<input type="button" value="-" class="minus" />'),
            c.children().last().after('<input type="button" value="+" class="plus" />')
    })
}

String.prototype.getDecimals || (String.prototype.getDecimals = function () {
    var a = this,
        b = ("" + a).match(/(?:\.(\d+))?(?:[eE]([+-]?\d+))?$/);
    return b ? Math.max(0, (b[1] ? b[1].length : 0) - (b[2] ? +b[2] : 0)) : 0
}), jQuery(document).ready(function () {
    wcqib_refresh_quantity_increments()
}), jQuery(document).on("updated_wc_div", function () {
    wcqib_refresh_quantity_increments()
}), jQuery(document).on("click", ".plus, .minus", function () {
    var a = jQuery(this).closest(".quantity").find('input[name="quantity"], input[name="quantity[]"]'),
        b = parseFloat(a.val()),
        c = parseFloat(a.attr("max")),
        d = parseFloat(a.attr("min")),
        e = a.attr("step");
    b && "" !== b && "NaN" !== b || (b = 0), "" !== c && "NaN" !== c || (c = ""), "" !== d && "NaN" !== d || (d = 0), "any" !== e && "" !== e && void 0 !== e && "NaN" !== parseFloat(e) || (e = 1), jQuery(this).is(".plus") ? c && b >= c ? a.val(c) : a.val((b + parseFloat(e)).toFixed(e.getDecimals())) : d && b <= d ? a.val(d) : b > 0 && a.val((b - parseFloat(e)).toFixed(e.getDecimals())), a.trigger("change")
});

$(document).on('click', 'input[name="quantity"], input[name="quantity[]"]', function (e) {
    // Allow: backspace, delete, tab, escape, enter and .
    if ($.inArray(e.keyCode, [46, 8, 9, 27, 13, 190]) !== -1 ||
        // Allow: Ctrl+A
        (e.keyCode == 65 && e.ctrlKey === true) ||
        // Allow: home, end, left, right
        (e.keyCode >= 35 && e.keyCode <= 39)) {
        // let it happen, don't do anything
        return;
    }
    // Ensure that it is a number and stop the keypress
    if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
        e.preventDefault();
    }
});


//for ai module
$(document).on('click', 'a[data-ajax-popup-over="true"], button[data-ajax-popup-over="true"], div[data-ajax-popup-over="true"]', function () {
    var validate = $(this).attr('data-validate');
    var id = '';
    if (validate) {
        id = $(validate).val();
    }
    var title_over = $(this).data('title');
    $('#commonModalOver .modal-dialog').removeClass('modal-sm modal-md modal-lg modal-xl modal-xxl');
    var size_over = ($(this).data('size') == '') ? 'md' : $(this).data('size');

    var url = $(this).data('url');
    $("#commonModalOver .modal-title").html(title_over);
    $("#commonModalOver .modal-dialog").addClass('modal-' + size_over);
    $.ajax({
        url: url + '?id=' + id,
        success: function (data) {
            $('#commonModalOver .modal-body').html(data);

            // إصلاح aria-hidden قبل فتح المودال
            $("#commonModalOver").removeAttr('aria-hidden');
            $("#commonModalOver").modal('show');

            taskCheckbox();
        },
        error: function (data) {
            data = data.responseJSON;
            show_toastr('Error', data.error, 'error')
        }
    });
});



//start input serach box
function JsSearchBox() {
    if ($(".js-searchBox").length)
    {
        $( ".js-searchBox" ).each(function( index ) {
            if($(this).parent().find('.formTextbox').length == 0)
            {
                $(this).searchBox({ elementWidth: '250'});
            }
        });
    }
}

$(document).ready(function() {
    JsSearchBox();

    function JsSearchBox() {
        if ($(".js-searchBox").length) {
            $(".js-searchBox").each(function(index) {
                if ($(this).parent().find('.formTextbox').length === 0) {
                    $(this).searchBox({ elementWidth: '250' });
                }
            });
        }
    }
});

//end input serach box



