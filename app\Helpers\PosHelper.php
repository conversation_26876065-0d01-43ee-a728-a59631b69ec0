<?php

namespace App\Helpers;

use App\Models\Utility;

class PosHelper
{
    /**
     * التحقق من إمكانية عرض صور المنتجات في POS
     */
    public static function shouldShowProductImages()
    {
        return config('pos_settings.show_product_images', true);
    }

    /**
     * التحقق من إمكانية عرض صور المنتجات في السلة
     */
    public static function shouldShowCartImages()
    {
        return config('pos_settings.show_cart_images', true);
    }

    /**
     * التحقق من إمكانية عرض صور المنتجات في قائمة المنتجات
     */
    public static function shouldShowProductListImages()
    {
        return config('pos_settings.show_product_list_images', true);
    }

    /**
     * الحصول على مسار الصورة الافتراضية
     */
    public static function getDefaultProductImage()
    {
        return asset(config('pos_settings.default_product_image', 'storage/uploads/pro_image/default.png'));
    }

    /**
     * الحصول على مسار صورة المنتج مع معالجة الأخطاء
     */
    public static function getProductImageUrl($product)
    {
        if (!self::shouldShowProductImages()) {
            return null;
        }

        if (!empty($product->pro_image)) {
            $imagePath = Utility::get_file('uploads/pro_image/' . $product->pro_image);
            if ($imagePath && file_exists(public_path($imagePath))) {
                return $imagePath;
            }
        }

        return self::getDefaultProductImage();
    }

    /**
     * إنشاء صورة افتراضية بالأحرف الأولى من اسم المنتج
     */
    public static function generateDefaultImageHtml($productName, $size = 50)
    {
        $initials = strtoupper(substr($productName, 0, 2));
        $colors = ['#007bff', '#28a745', '#dc3545', '#ffc107', '#17a2b8', '#6f42c1'];
        $color = $colors[crc32($productName) % count($colors)];

        return '<div class="default-product-image d-flex align-items-center justify-content-center" 
                     style="width: ' . $size . 'px; height: ' . $size . 'px; 
                            background: linear-gradient(45deg, ' . $color . ', ' . self::adjustBrightness($color, -20) . '); 
                            color: white; font-weight: bold; border-radius: 8px; font-size: ' . ($size * 0.3) . 'px;">
                    ' . $initials . '
                </div>';
    }

    /**
     * تعديل سطوع اللون
     */
    private static function adjustBrightness($hex, $steps)
    {
        $steps = max(-255, min(255, $steps));
        $hex = str_replace('#', '', $hex);
        
        if (strlen($hex) == 3) {
            $hex = str_repeat(substr($hex, 0, 1), 2) . str_repeat(substr($hex, 1, 1), 2) . str_repeat(substr($hex, 2, 1), 2);
        }

        $color_parts = str_split($hex, 2);
        $return = '#';

        foreach ($color_parts as $color) {
            $color = hexdec($color);
            $color = max(0, min(255, $color + $steps));
            $return .= str_pad(dechex($color), 2, '0', STR_PAD_LEFT);
        }

        return $return;
    }

    /**
     * التحقق من صحة بيانات السلة
     */
    public static function validateCartData($cartData)
    {
        $requiredFields = ['name', 'price', 'quantity'];
        
        foreach ($requiredFields as $field) {
            if (!isset($cartData[$field]) || empty($cartData[$field])) {
                return false;
            }
        }

        return true;
    }

    /**
     * تنظيف بيانات السلة من البيانات الناقصة
     */
    public static function cleanCartData($cartSession)
    {
        if (!is_array($cartSession)) {
            return [];
        }

        $cleanedCart = [];
        foreach ($cartSession as $key => $item) {
            if (self::validateCartData($item)) {
                $cleanedCart[$key] = $item;
            }
        }

        return $cleanedCart;
    }

    /**
     * الحصول على طرق الدفع المتاحة
     */
    public static function getAvailablePaymentMethods()
    {
        $methods = config('pos_settings.payment_methods', []);
        $available = [];

        foreach ($methods as $key => $method) {
            if ($method['enabled'] ?? true) {
                $available[$key] = $method;
            }
        }

        return $available;
    }

    /**
     * التحقق من إمكانية الطباعة الحرارية
     */
    public static function isThermalPrintEnabled()
    {
        return config('pos_settings.thermal_print.enabled', true);
    }

    /**
     * تنسيق رقم الفاتورة
     */
    public static function formatInvoiceNumber($number, $prefix = 'POS')
    {
        return $prefix . '-' . str_pad($number, 6, '0', STR_PAD_LEFT);
    }

    /**
     * حساب الضريبة
     */
    public static function calculateTax($amount, $taxRate)
    {
        return ($amount * $taxRate) / 100;
    }

    /**
     * حساب الخصم
     */
    public static function calculateDiscount($amount, $discountRate, $isPercentage = true)
    {
        if ($isPercentage) {
            return ($amount * $discountRate) / 100;
        }
        
        return $discountRate;
    }

    /**
     * تنسيق المبلغ
     */
    public static function formatAmount($amount, $currency = null)
    {
        if (!$currency) {
            $settings = Utility::settings();
            $currency = $settings['site_currency_symbol'] ?? '$';
        }

        return $currency . ' ' . number_format($amount, 2);
    }
}
