// ملف تشخيص زر الدفع في POS
console.log('=== POS Payment Button Debug Script ===');

// فحص وجود العناصر المطلوبة
function checkElements() {
    console.log('--- Checking Elements ---');
    
    // فحص زر الدفع
    var paymentButton = $('#payment');
    console.log('Payment button exists:', paymentButton.length > 0);
    console.log('Payment button HTML:', paymentButton.length > 0 ? paymentButton[0].outerHTML : 'Not found');
    
    if (paymentButton.length > 0) {
        console.log('Payment button disabled:', paymentButton.prop('disabled'));
        console.log('Payment button classes:', paymentButton.attr('class'));
        console.log('Payment button data-url:', paymentButton.data('url'));
        console.log('Payment button data-ajax-popup:', paymentButton.data('ajax-popup'));
    }
    
    // فحص الحقول المخفية
    console.log('Customer hidden field value:', $('#vc_name_hidden').val());
    console.log('Warehouse hidden field value:', $('#warehouse_name_hidden').val());
    console.log('Discount hidden field value:', $('#discount_hidden').val());
    
    // فحص السلة
    var cartItems = $('#tbody tr:not(.no-found)');
    console.log('Cart items count:', cartItems.length);
    
    // فحص المودال
    var modal = $('#commonModal');
    console.log('Modal exists:', modal.length > 0);
}

// فحص معالجات الأحداث
function checkEventHandlers() {
    console.log('--- Checking Event Handlers ---');
    
    // فحص معالجات jQuery
    var paymentButton = $('#payment');
    if (paymentButton.length > 0) {
        var events = $._data(paymentButton[0], 'events');
        console.log('Payment button events:', events);
    }
    
    // فحص معالجات data-ajax-popup
    var ajaxPopupElements = $('[data-ajax-popup="true"]');
    console.log('Elements with data-ajax-popup:', ajaxPopupElements.length);
}

// اختبار النقر على الزر
function testButtonClick() {
    console.log('--- Testing Button Click ---');
    
    var paymentButton = $('#payment');
    if (paymentButton.length > 0) {
        console.log('Attempting to trigger click event...');
        
        // محاكاة النقر
        paymentButton.trigger('click');
        
        setTimeout(function() {
            console.log('Click event triggered');
        }, 100);
    } else {
        console.log('Payment button not found!');
    }
}

// فحص مكتبات JavaScript المطلوبة
function checkLibraries() {
    console.log('--- Checking Libraries ---');
    
    console.log('jQuery loaded:', typeof $ !== 'undefined');
    console.log('jQuery version:', typeof $ !== 'undefined' ? $.fn.jquery : 'Not loaded');
    console.log('Bootstrap loaded:', typeof bootstrap !== 'undefined');
}

// فحص الأخطاء في وحدة التحكم
function checkConsoleErrors() {
    console.log('--- Console Errors Check ---');
    
    // إضافة معالج للأخطاء
    window.addEventListener('error', function(e) {
        console.error('JavaScript Error detected:', {
            message: e.message,
            filename: e.filename,
            lineno: e.lineno,
            colno: e.colno
        });
    });
}

// تشغيل جميع الفحوصات
function runAllChecks() {
    console.log('Starting POS Payment Button Diagnosis...');
    
    checkLibraries();
    checkElements();
    checkEventHandlers();
    checkConsoleErrors();
    
    console.log('=== Diagnosis Complete ===');
    console.log('To test button click, run: testButtonClick()');
}

// تشغيل الفحوصات عند تحميل الصفحة
$(document).ready(function() {
    setTimeout(runAllChecks, 1000); // انتظار ثانية واحدة للتأكد من تحميل كل شيء
});

// إضافة دوال للوحة التحكم
window.debugPOS = {
    checkElements: checkElements,
    checkEventHandlers: checkEventHandlers,
    testButtonClick: testButtonClick,
    runAllChecks: runAllChecks
};

console.log('Debug functions available: window.debugPOS');
