<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Employee;
use App\Models\AttendanceEmployee;
use App\Models\TrackPhoto;
use App\Models\Branch;
use App\Models\Department;
use App\Models\warehouse;
use App\Models\Utility;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class MobileAppController extends Controller
{
    use ApiResponser;

    /**
     * تسجيل دخول للتطبيق
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|string|email',
            'password' => 'required|string'
        ]);

        if ($validator->fails()) {
            return $this->error($validator->errors()->first(), 422);
        }

        if (!Auth::attempt($request->only('email', 'password'))) {
            return $this->error('بيانات الدخول غير صحيحة', 401);
        }

        $user = Auth::user();
        $employee = Employee::where('user_id', $user->id)->first();
        
        // تحديث آخر تسجيل دخول
        $user->update(['last_login_at' => now()]);

        $token = $user->createToken('MobileApp')->plainTextToken;

        return $this->success([
            'token' => $token,
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'type' => $user->type,
                'warehouse_id' => $user->warehouse_id
            ],
            'employee' => $employee,
            'permissions' => $user->getAllPermissions()->pluck('name'),
            'company_settings' => $this->getCompanySettings($user->creatorId())
        ], 'تم تسجيل الدخول بنجاح');
    }

    /**
     * تسجيل خروج
     */
    public function logout()
    {
        auth()->user()->tokens()->delete();
        return $this->success([], 'تم تسجيل الخروج بنجاح');
    }

    /**
     * تسجيل الحضور
     */
    public function clockIn(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'address' => 'nullable|string',
            'photo' => 'nullable|string' // base64 encoded image
        ]);

        if ($validator->fails()) {
            return $this->error($validator->errors()->first(), 422);
        }

        $user = auth()->user();
        $employee = Employee::where('user_id', $user->id)->first();
        
        if (!$employee) {
            return $this->error('لم يتم العثور على بيانات الموظف', 404);
        }

        $date = date('Y-m-d');
        $time = date('H:i:s');
        
        // التحقق من وجود تسجيل حضور لليوم
        $existingAttendance = AttendanceEmployee::where('employee_id', $employee->id)
            ->where('date', $date)
            ->first();
            
        if ($existingAttendance) {
            return $this->error('تم تسجيل الحضور مسبقاً لهذا اليوم', 400);
        }

        // حساب التأخير
        $startTime = Utility::getValByName('company_start_time') ?: '09:00:00';
        $totalLateSeconds = strtotime($time) - strtotime($startTime);
        $late = $totalLateSeconds > 0 ? gmdate('H:i:s', $totalLateSeconds) : '00:00:00';

        // إنشاء سجل الحضور
        $attendance = AttendanceEmployee::create([
            'employee_id' => $employee->id,
            'date' => $date,
            'status' => 'Present',
            'clock_in' => $time,
            'clock_out' => '00:00:00',
            'late' => $late,
            'early_leaving' => '00:00:00',
            'overtime' => '00:00:00',
            'total_rest' => '00:00:00',
            'created_by' => $user->id
        ]);

        // حفظ الموقع والصورة في track_photos
        $this->saveLocationAndPhoto($user->id, $request, 'clock_in');

        return $this->success([
            'attendance' => $attendance,
            'message' => 'تم تسجيل الحضور بنجاح',
            'clock_in_time' => $time,
            'late_duration' => $late
        ], 'تم تسجيل الحضور بنجاح');
    }

    /**
     * تسجيل الانصراف
     */
    public function clockOut(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'address' => 'nullable|string',
            'photo' => 'nullable|string' // base64 encoded image
        ]);

        if ($validator->fails()) {
            return $this->error($validator->errors()->first(), 422);
        }

        $user = auth()->user();
        $employee = Employee::where('user_id', $user->id)->first();
        
        if (!$employee) {
            return $this->error('لم يتم العثور على بيانات الموظف', 404);
        }

        $date = date('Y-m-d');
        $time = date('H:i:s');
        
        // البحث عن سجل الحضور لليوم
        $attendance = AttendanceEmployee::where('employee_id', $employee->id)
            ->where('date', $date)
            ->where('clock_out', '00:00:00')
            ->first();
            
        if (!$attendance) {
            return $this->error('لم يتم العثور على تسجيل حضور لهذا اليوم', 400);
        }

        // حساب الإضافي والانصراف المبكر
        $endTime = Utility::getValByName('company_end_time') ?: '17:00:00';
        $totalOvertimeSeconds = strtotime($time) - strtotime($endTime);
        $overtime = $totalOvertimeSeconds > 0 ? gmdate('H:i:s', $totalOvertimeSeconds) : '00:00:00';
        
        $totalEarlySeconds = strtotime($endTime) - strtotime($time);
        $earlyLeaving = $totalEarlySeconds > 0 ? gmdate('H:i:s', $totalEarlySeconds) : '00:00:00';

        // تحديث سجل الحضور
        $attendance->update([
            'clock_out' => $time,
            'overtime' => $overtime,
            'early_leaving' => $earlyLeaving
        ]);

        // حفظ الموقع والصورة
        $this->saveLocationAndPhoto($user->id, $request, 'clock_out');

        return $this->success([
            'attendance' => $attendance,
            'message' => 'تم تسجيل الانصراف بنجاح',
            'clock_out_time' => $time,
            'overtime_duration' => $overtime,
            'early_leaving_duration' => $earlyLeaving
        ], 'تم تسجيل الانصراف بنجاح');
    }

    /**
     * تحديث الموقع (للتتبع المستمر)
     */
    public function updateLocation(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
            'address' => 'nullable|string',
            'notes' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return $this->error($validator->errors()->first(), 422);
        }

        $user = auth()->user();
        
        // حفظ الموقع في track_photos (استخدام الجدول الموجود)
        $this->saveLocationAndPhoto($user->id, $request, 'tracking');

        return $this->success([], 'تم تحديث الموقع بنجاح');
    }

    /**
     * الحصول على حضور اليوم
     */
    public function getTodayAttendance()
    {
        $user = auth()->user();
        $employee = Employee::where('user_id', $user->id)->first();
        
        if (!$employee) {
            return $this->error('لم يتم العثور على بيانات الموظف', 404);
        }

        $attendance = AttendanceEmployee::where('employee_id', $employee->id)
            ->where('date', date('Y-m-d'))
            ->first();

        return $this->success([
            'attendance' => $attendance,
            'has_clocked_in' => $attendance ? true : false,
            'has_clocked_out' => $attendance && $attendance->clock_out != '00:00:00' ? true : false
        ], 'تم جلب بيانات حضور اليوم');
    }

    /**
     * الحصول على تاريخ الحضور
     */
    public function getAttendanceHistory(Request $request)
    {
        $user = auth()->user();
        $employee = Employee::where('user_id', $user->id)->first();
        
        if (!$employee) {
            return $this->error('لم يتم العثور على بيانات الموظف', 404);
        }

        $limit = $request->get('limit', 30);
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');

        $query = AttendanceEmployee::where('employee_id', $employee->id);
        
        if ($startDate && $endDate) {
            $query->whereBetween('date', [$startDate, $endDate]);
        }

        $attendance = $query->orderBy('date', 'desc')
            ->limit($limit)
            ->get();

        return $this->success($attendance, 'تم جلب تاريخ الحضور');
    }

    /**
     * الحصول على الملف الشخصي
     */
    public function getProfile()
    {
        $user = auth()->user();
        $employee = Employee::where('user_id', $user->id)
            ->with(['branch', 'department'])
            ->first();
        
        return $this->success([
            'user' => $user,
            'employee' => $employee
        ], 'تم جلب الملف الشخصي');
    }

    /**
     * حفظ الموقع والصورة في track_photos
     */
    private function saveLocationAndPhoto($userId, $request, $type)
    {
        $photoPath = null;
        
        // حفظ الصورة إذا كانت موجودة
        if ($request->has('photo') && !empty($request->photo)) {
            $photoPath = $this->saveBase64Image($request->photo, $userId, $type);
        }

        // إنشاء سجل في track_photos مع بيانات الموقع
        TrackPhoto::create([
            'track_id' => 0, // نستخدم 0 للتمييز عن التتبع العادي
            'user_id' => $userId,
            'img_path' => $photoPath,
            'time' => json_encode([
                'timestamp' => now()->toISOString(),
                'latitude' => $request->latitude,
                'longitude' => $request->longitude,
                'address' => $request->address ?? '',
                'type' => $type,
                'notes' => $request->notes ?? ''
            ]),
            'status' => 1
        ]);
    }

    /**
     * حفظ صورة base64
     */
    private function saveBase64Image($base64Image, $userId, $type)
    {
        try {
            $image = base64_decode($base64Image);
            $fileName = $type . '_' . $userId . '_' . time() . '.jpg';
            $path = 'uploads/attendance_photos/' . date('Y/m/d') . '/' . $fileName;
            
            Storage::disk('public')->put($path, $image);
            
            return $path;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * الحصول على إعدادات الشركة
     */
    private function getCompanySettings($creatorId)
    {
        $settings = Utility::settings($creatorId);
        
        return [
            'company_name' => $settings['company_name'] ?? '',
            'company_start_time' => $settings['company_start_time'] ?? '09:00:00',
            'company_end_time' => $settings['company_end_time'] ?? '17:00:00',
            'tracking_interval' => $settings['tracking_interval'] ?? 300, // 5 minutes
        ];
    }
}
