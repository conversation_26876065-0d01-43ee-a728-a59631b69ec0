<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Employee;
use App\Models\AttendanceEmployee;
use App\Models\TrackPhoto;
use App\Models\Branch;
use App\Models\Department;
use App\Models\warehouse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class EmployeeTrackingController extends Controller
{
    /**
     * عرض شاشة تتبع الموظفين الرئيسية
     */
    public function index(Request $request)
    {
        if (!Auth::user()->can('manage employee tracking')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $branches = Branch::where('created_by', Auth::user()->creatorId())->get();
        $departments = Department::where('created_by', Auth::user()->creatorId())->get();
        $warehouses = warehouse::where('created_by', Auth::user()->creatorId())->get();

        // فلترة البيانات
        $selectedBranch = $request->get('branch_id');
        $selectedDepartment = $request->get('department_id');
        $selectedDate = $request->get('date', date('Y-m-d'));

        $employeesQuery = Employee::where('created_by', Auth::user()->creatorId())
            ->with(['branch', 'department', 'user']);

        if ($selectedBranch) {
            $employeesQuery->where('branch_id', $selectedBranch);
        }

        if ($selectedDepartment) {
            $employeesQuery->where('department_id', $selectedDepartment);
        }

        $employees = $employeesQuery->get();

        // جلب بيانات الحضور لليوم المحدد
        $attendanceData = [];
        foreach ($employees as $employee) {
            $attendance = AttendanceEmployee::where('employee_id', $employee->id)
                ->where('date', $selectedDate)
                ->first();

            $attendanceData[$employee->id] = [
                'employee' => $employee,
                'attendance' => $attendance,
                'locations' => $this->getEmployeeLocations($employee->user_id, $selectedDate)
            ];
        }

        return view('employee_tracking.index', compact(
            'attendanceData',
            'branches',
            'departments',
            'warehouses',
            'selectedBranch',
            'selectedDepartment',
            'selectedDate'
        ));
    }

    /**
     * عرض تفاصيل تتبع موظف محدد
     */
    public function show($employeeId, Request $request)
    {
        if (!Auth::user()->can('view employee tracking')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $employee = Employee::where('id', $employeeId)
            ->where('created_by', Auth::user()->creatorId())
            ->with(['branch', 'department', 'user'])
            ->firstOrFail();

        $selectedDate = $request->get('date', date('Y-m-d'));
        $startDate = $request->get('start_date', date('Y-m-d', strtotime('-7 days')));
        $endDate = $request->get('end_date', date('Y-m-d'));

        // جلب بيانات الحضور
        $attendance = AttendanceEmployee::where('employee_id', $employee->id)
            ->where('date', $selectedDate)
            ->first();

        // جلب تاريخ الحضور
        $attendanceHistory = AttendanceEmployee::where('employee_id', $employee->id)
            ->whereBetween('date', [$startDate, $endDate])
            ->orderBy('date', 'desc')
            ->get();

        // جلب مواقع اليوم
        $todayLocations = $this->getEmployeeLocations($employee->user_id, $selectedDate);

        // جلب إحصائيات الحضور
        $attendanceStats = $this->getAttendanceStats($employee->id, $startDate, $endDate);

        return view('employee_tracking.show', compact(
            'employee',
            'attendance',
            'attendanceHistory',
            'todayLocations',
            'attendanceStats',
            'selectedDate',
            'startDate',
            'endDate'
        ));
    }

    /**
     * عرض الخريطة المباشرة لجميع الموظفين
     */
    public function liveMap(Request $request)
    {
        if (!Auth::user()->can('view live tracking')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $employees = Employee::where('created_by', Auth::user()->creatorId())
            ->with(['user', 'branch', 'department'])
            ->get();

        $liveLocations = [];
        foreach ($employees as $employee) {
            $latestLocation = $this->getLatestLocation($employee->user_id);
            if ($latestLocation) {
                $liveLocations[] = [
                    'employee' => $employee,
                    'location' => $latestLocation,
                    'is_online' => $this->isEmployeeOnline($latestLocation)
                ];
            }
        }

        return view('employee_tracking.live_map', compact('liveLocations'));
    }

    /**
     * تقرير الحضور والانصراف
     */
    public function attendanceReport(Request $request)
    {
        if (!Auth::user()->can('view attendance reports')) {
            return redirect()->back()->with('error', __('Permission denied.'));
        }

        $startDate = $request->get('start_date', date('Y-m-01')); // بداية الشهر
        $endDate = $request->get('end_date', date('Y-m-d'));
        $branchId = $request->get('branch_id');
        $departmentId = $request->get('department_id');

        $query = Employee::where('created_by', Auth::user()->creatorId())
            ->with(['branch', 'department', 'user']);

        if ($branchId) {
            $query->where('branch_id', $branchId);
        }

        if ($departmentId) {
            $query->where('department_id', $departmentId);
        }

        $employees = $query->get();

        $reportData = [];
        foreach ($employees as $employee) {
            $attendanceRecords = AttendanceEmployee::where('employee_id', $employee->id)
                ->whereBetween('date', [$startDate, $endDate])
                ->get();

            $stats = [
                'total_days' => $attendanceRecords->count(),
                'present_days' => $attendanceRecords->where('status', 'Present')->count(),
                'late_days' => $attendanceRecords->where('late', '!=', '00:00:00')->count(),
                'total_late_time' => $this->calculateTotalTime($attendanceRecords, 'late'),
                'total_overtime' => $this->calculateTotalTime($attendanceRecords, 'overtime'),
                'early_leaving_days' => $attendanceRecords->where('early_leaving', '!=', '00:00:00')->count(),
            ];

            $reportData[] = [
                'employee' => $employee,
                'stats' => $stats,
                'attendance_records' => $attendanceRecords
            ];
        }

        $branches = Branch::where('created_by', Auth::user()->creatorId())->get();
        $departments = Department::where('created_by', Auth::user()->creatorId())->get();

        return view('employee_tracking.attendance_report', compact(
            'reportData',
            'branches',
            'departments',
            'startDate',
            'endDate',
            'branchId',
            'departmentId'
        ));
    }

    /**
     * تصدير تقرير الحضور
     */
    public function exportAttendanceReport(Request $request)
    {
        // يمكن إضافة تصدير Excel أو PDF هنا
        return response()->json(['message' => 'Export functionality to be implemented']);
    }

    /**
     * الحصول على مواقع الموظف لتاريخ محدد
     */
    private function getEmployeeLocations($userId, $date)
    {
        return TrackPhoto::where('user_id', $userId)
            ->where('created_at', '>=', $date . ' 00:00:00')
            ->where('created_at', '<=', $date . ' 23:59:59')
            ->orderBy('created_at', 'asc')
            ->get()
            ->map(function ($photo) {
                $timeData = json_decode($photo->time, true);
                return [
                    'id' => $photo->id,
                    'timestamp' => $timeData['timestamp'] ?? $photo->created_at,
                    'latitude' => $timeData['latitude'] ?? null,
                    'longitude' => $timeData['longitude'] ?? null,
                    'address' => $timeData['address'] ?? '',
                    'type' => $timeData['type'] ?? 'tracking',
                    'photo_path' => $photo->img_path,
                    'notes' => $timeData['notes'] ?? ''
                ];
            });
    }

    /**
     * الحصول على آخر موقع للموظف
     */
    private function getLatestLocation($userId)
    {
        $latestPhoto = TrackPhoto::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$latestPhoto) {
            return null;
        }

        $timeData = json_decode($latestPhoto->time, true);
        return [
            'timestamp' => $timeData['timestamp'] ?? $latestPhoto->created_at,
            'latitude' => $timeData['latitude'] ?? null,
            'longitude' => $timeData['longitude'] ?? null,
            'address' => $timeData['address'] ?? '',
            'type' => $timeData['type'] ?? 'tracking'
        ];
    }

    /**
     * التحقق من كون الموظف متصل (آخر موقع خلال 10 دقائق)
     */
    private function isEmployeeOnline($location)
    {
        if (!$location || !isset($location['timestamp'])) {
            return false;
        }

        $lastUpdate = Carbon::parse($location['timestamp']);
        return $lastUpdate->diffInMinutes(now()) <= 10;
    }

    /**
     * الحصول على إحصائيات الحضور
     */
    private function getAttendanceStats($employeeId, $startDate, $endDate)
    {
        $records = AttendanceEmployee::where('employee_id', $employeeId)
            ->whereBetween('date', [$startDate, $endDate])
            ->get();

        return [
            'total_days' => $records->count(),
            'present_days' => $records->where('status', 'Present')->count(),
            'late_days' => $records->where('late', '!=', '00:00:00')->count(),
            'total_late_time' => $this->calculateTotalTime($records, 'late'),
            'total_overtime' => $this->calculateTotalTime($records, 'overtime'),
            'average_clock_in' => $this->calculateAverageTime($records, 'clock_in'),
            'average_clock_out' => $this->calculateAverageTime($records, 'clock_out')
        ];
    }

    /**
     * حساب إجمالي الوقت
     */
    private function calculateTotalTime($records, $field)
    {
        $totalSeconds = 0;
        foreach ($records as $record) {
            if ($record->$field && $record->$field != '00:00:00') {
                $time = explode(':', $record->$field);
                $totalSeconds += ($time[0] * 3600) + ($time[1] * 60) + $time[2];
            }
        }
        
        $hours = floor($totalSeconds / 3600);
        $minutes = floor(($totalSeconds % 3600) / 60);
        $seconds = $totalSeconds % 60;
        
        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }

    /**
     * حساب متوسط الوقت
     */
    private function calculateAverageTime($records, $field)
    {
        $validRecords = $records->where($field, '!=', '00:00:00')->where($field, '!=', null);
        
        if ($validRecords->count() == 0) {
            return '00:00:00';
        }

        $totalSeconds = 0;
        foreach ($validRecords as $record) {
            $time = explode(':', $record->$field);
            $totalSeconds += ($time[0] * 3600) + ($time[1] * 60) + $time[2];
        }
        
        $averageSeconds = $totalSeconds / $validRecords->count();
        $hours = floor($averageSeconds / 3600);
        $minutes = floor(($averageSeconds % 3600) / 60);
        $seconds = floor($averageSeconds % 60);
        
        return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }
}
