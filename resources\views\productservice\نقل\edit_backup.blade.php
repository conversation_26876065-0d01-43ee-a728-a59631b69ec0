{{Form::model($productService,array('route' => array('productservice.update', $productService->id), 'method' => 'PUT','enctype' => "multipart/form-data", 'class'=>'needs-validation', 'novalidate')) }}
<div class="modal-body">
    {{-- start for ai module--}}
    @php
        $plan= \App\Models\Utility::getChatGPTSettings();
    @endphp
    @if($plan->chatgpt == 1)
    <div class="text-end">
        <a href="#" data-size="md" class="btn  btn-primary btn-icon btn-sm" data-ajax-popup-over="true" data-url="{{ route('generate',['productservice']) }}"
           data-bs-placement="top" data-title="{{ __('Generate content with AI') }}">
            <i class="fas fa-robot"></i> <span>{{__('Generate with AI')}}</span>
        </a>
    </div>
    @endif
    {{-- end for ai module--}}
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('name', __('Name'),['class'=>'form-label']) }}<x-required></x-required>
                {{ Form::text('name', null, array('class' => 'form-control','required'=>'required', 'placeholder' => __('Enter Product Name'))) }}
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('sku', __('SKU'),['class'=>'form-label']) }}<x-required></x-required>
                {{ Form::text('sku', null, array('class' => 'form-control','required'=>'required', 'placeholder' => __('Enter SKU'))) }}
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('sale_value_helper', __('Sale Value (Including VAT)'),['class'=>'form-label']) }}
                <small class="text-muted d-block">{{ __('Enter the total amount including VAT - the system will extract the VAT and show the price before VAT') }}</small>
                @php
                    // Calculate sale value including VAT from current sale price and taxes
                    $currentSalePrice = $productService->sale_price ?? 0;
                    $currentTaxRate = 0;
                    try {
                        if (!empty($productService->tax_id)) {
                            $currentTaxRate = \App\Models\Utility::totalTaxRate($productService->tax_id);
                        }
                    } catch (Exception $e) {
                        $currentTaxRate = 0;
                    }
                    // Calculate total amount including VAT: sale_price * (1 + tax_rate/100)
                    $saleValueIncludingVAT = $currentTaxRate > 0 ? $currentSalePrice * (1 + ($currentTaxRate / 100)) : $currentSalePrice;
                @endphp
                {{ Form::number('sale_value_helper', number_format($saleValueIncludingVAT, 2, '.', ''), array('class' => 'form-control','step'=>'0.01', 'placeholder' => __('Enter Total Amount Including VAT'), 'id' => 'sale_value_helper')) }}
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('sale_price', __('Sale Price (Before VAT)'),['class'=>'form-label']) }}<x-required></x-required>
                {{ Form::number('sale_price', null, array('class' => 'form-control','required'=>'required','step'=>'0.01', 'placeholder' => __('Enter Sale Price'), 'id' => 'sale_price')) }}
                <small class="text-muted">{{ __('This will be calculated automatically when you enter the total amount above') }}</small>
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('purchase_price', __('Purchase Price'),['class'=>'form-label']) }}<x-required></x-required>
                {{ Form::number('purchase_price', null, array('class' => 'form-control','required'=>'required','step'=>'0.01', 'placeholder' => __('Enter Purchase Price'))) }}
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('tax_id', __('Tax'),['class'=>'form-label']) }}
                {{ Form::select('tax_id[]', $taxes,explode(',',$productService->tax_id), array('class' => 'form-control choices','id'=>'choices-multiple1','multiple'=>'','placeholder' => __('Select Tax'))) }}
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('category_id', __('Category'),['class'=>'form-label']) }}<x-required></x-required>
                {{ Form::select('category_id', $category,$productService->category_id, array('class' => 'form-control','required'=>'required','placeholder' => __('Select Category'))) }}
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                {{ Form::label('unit_id', __('Unit'),['class'=>'form-label']) }}<x-required></x-required>
                {{ Form::select('unit_id', $unit,$productService->unit_id, array('class' => 'form-control','required'=>'required','placeholder' => __('Select Unit'))) }}
            </div>
        </div>
        <div class="col-md-12">
            <div class="form-group">
                {{ Form::label('type', __('Type'),['class'=>'form-label']) }}<x-required></x-required>
                {{ Form::select('type', $productType,$productService->type, array('class' => 'form-control type','required'=>'required','placeholder' => __('Select Type'))) }}
            </div>
        </div>
        <div class="col-md-12 quantity {{($productService->type=='service')?'d-none':''}}">
            <div class="form-group">
                {{ Form::label('quantity', __('Quantity'),['class'=>'form-label']) }}<span class="text-danger">*</span>
                {{ Form::number('quantity', null, array('class' => 'form-control','step'=>'0.01', 'placeholder' => __('Enter Quantity'))) }}
            </div>
        </div>
        <div class="col-md-12">
            <div class="form-group">
                {{ Form::label('description', __('Description'),['class'=>'form-label']) }}
                {{ Form::textarea('description', null, array('class' => 'form-control','rows'=>'3', 'placeholder' => __('Enter Description'))) }}
            </div>
        </div>
    </div>
</div>
<div class="modal-footer">
    <input type="button" value="{{__('Cancel')}}" class="btn  btn-light" data-bs-dismiss="modal">
    <input type="submit" value="{{__('Update')}}" class="btn  btn-primary">
</div>
{{ Form::close() }}

<script>
    $(document).ready(function() {
        // Calculate sale price (before VAT) from sale value helper (including VAT)
        function calculateSalePrice() {
            var saleValueHelper = parseFloat($('#sale_value_helper').val()) || 0;
            
            if (saleValueHelper <= 0) {
                $('#sale_price').val('');
                return;
            }

            var selectedTaxes = $('#choices-multiple1').val() || [];
            
            if (selectedTaxes.length > 0) {
                // Get tax rates via AJAX
                $.ajax({
                    url: '{{ route("get.tax.rates") }}',
                    type: 'POST',
                    data: {
                        '_token': '{{ csrf_token() }}',
                        'tax_ids': selectedTaxes
                    },
                    success: function(response) {
                        if (response.success) {
                            var totalTaxRate = response.total_rate || 0;
                            
                            // Calculate sale price before VAT: total_amount / (1 + tax_rate/100)
                            var salePriceBeforeVAT = saleValueHelper / (1 + (totalTaxRate / 100));
                            $('#sale_price').val(salePriceBeforeVAT.toFixed(2));
                        }
                    },
                    error: function() {
                        console.log('Error getting tax rates');
                        // If AJAX fails, just use the sale value as sale price
                        $('#sale_price').val(saleValueHelper.toFixed(2));
                    }
                });
            } else {
                // No tax selected, sale price equals sale value (no VAT to extract)
                $('#sale_price').val(saleValueHelper.toFixed(2));
            }
        }

        // Trigger calculation when sale value helper changes
        $(document).on('input', '#sale_value_helper', function() {
            calculateSalePrice();
        });

        // Trigger calculation when tax selection changes
        $('#choices-multiple1').on('change', function() {
            calculateSalePrice();
        });
    });
</script>
