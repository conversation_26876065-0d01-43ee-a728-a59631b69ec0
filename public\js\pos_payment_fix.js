/**
 * إصلاح زر الدفع في POS الكلاسيكي
 * هذا الملف يحل مشكلة عدم عمل زر الدفع
 */

(function() {
    'use strict';
    
    console.log('POS Payment Fix Script Loaded');
    
    // دالة آمنة لعرض الرسائل
    function safeShowToastr(type, message, icon) {
        try {
            if (typeof window.show_toastr === 'function' && window.show_toastr !== safeShowToastr) {
                window.show_toastr(type, message, icon);
            } else {
                console.log('[' + type.toUpperCase() + '] ' + message);
                
                // استخدام Bootstrap toast إذا كان متوفر
                if (typeof bootstrap !== 'undefined' && document.getElementById('liveToast')) {
                    var toastEl = document.getElementById('liveToast');
                    var toastBody = toastEl.querySelector('.toast-body');
                    if (toastBody) {
                        toastBody.textContent = message;
                        toastEl.className = 'toast text-white fade show bg-' + (type === 'error' ? 'danger' : 'success');
                        var toast = new bootstrap.Toast(toastEl);
                        toast.show();
                    }
                } else {
                    // استخدام alert كبديل أخير
                    alert(message);
                }
            }
        } catch (e) {
            console.error('Error in safeShowToastr:', e);
            alert(message);
        }
    }
    
    // معالج محسن لزر الدفع
    function handlePaymentButtonClick(e) {
        console.log('Payment button clicked - Enhanced handler');
        
        try {
            // التحقق من وجود منتجات في السلة
            var cartItems = document.querySelectorAll('#tbody tr:not(.no-found)');
            console.log('Cart items found:', cartItems.length);
            
            if (cartItems.length === 0) {
                e.preventDefault();
                e.stopPropagation();
                safeShowToastr('error', 'يرجى إضافة منتجات إلى السلة أولاً!', 'error');
                return false;
            }
            
            // التحقق من البيانات المطلوبة
            var customerId = document.getElementById('vc_name_hidden');
            var warehouseId = document.getElementById('warehouse_name_hidden');
            
            console.log('Customer ID element:', customerId);
            console.log('Warehouse ID element:', warehouseId);
            
            if (!customerId || !customerId.value || customerId.value === '0') {
                e.preventDefault();
                e.stopPropagation();
                safeShowToastr('error', 'يرجى اختيار عميل أولاً!', 'error');
                return false;
            }
            
            if (!warehouseId || !warehouseId.value || warehouseId.value === '0') {
                e.preventDefault();
                e.stopPropagation();
                safeShowToastr('error', 'يرجى اختيار مستودع أولاً!', 'error');
                return false;
            }
            
            console.log('Payment validation passed, proceeding...');
            return true;
            
        } catch (error) {
            console.error('Error in payment button handler:', error);
            e.preventDefault();
            e.stopPropagation();
            safeShowToastr('error', 'حدث خطأ أثناء معالجة الطلب', 'error');
            return false;
        }
    }
    
    // معالج AJAX محسن
    function handleAjaxPopup(e) {
        console.log('AJAX Popup handler triggered');
        
        try {
            var element = e.currentTarget;
            var id = element.id;
            var url = element.getAttribute('data-url');
            var title = element.getAttribute('data-title');
            
            console.log('Element details:', { id: id, url: url, title: title });
            
            if (id === 'payment') {
                // تشغيل فحص زر الدفع أولاً
                if (!handlePaymentButtonClick(e)) {
                    return false;
                }
            }
            
            if (!url) {
                console.error('No URL found for AJAX popup');
                safeShowToastr('error', 'رابط الصفحة غير موجود', 'error');
                return false;
            }
            
            // جمع البيانات
            var data = {};
            
            var elements = [
                { id: 'vc_name_hidden', key: 'vc_name' },
                { id: 'delivery_user_hidden', key: 'user_id' },
                { id: 'warehouse_name_hidden', key: 'warehouse_name' },
                { id: 'discount_hidden', key: 'discount' },
                { id: 'quotation_id', key: 'quotation_id' }
            ];
            
            elements.forEach(function(elem) {
                var el = document.getElementById(elem.id);
                if (el && el.value) {
                    data[elem.key] = el.value;
                }
            });
            
            console.log('Data to send:', data);
            
            // إعداد المودال
            var modal = document.getElementById('commonModal');
            var modalTitle = modal.querySelector('.modal-title');
            var modalBody = modal.querySelector('.modal-body');
            
            if (modalTitle) modalTitle.textContent = title || 'POS Invoice';
            if (modalBody) modalBody.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div><p>جاري التحميل...</p></div>';
            
            // إظهار المودال
            if (typeof bootstrap !== 'undefined') {
                var bsModal = new bootstrap.Modal(modal);
                bsModal.show();
            } else if (typeof $ !== 'undefined') {
                $(modal).modal('show');
            }
            
            // إرسال طلب AJAX
            var xhr = new XMLHttpRequest();
            xhr.open('GET', url + '?' + new URLSearchParams(data), true);
            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
            
            // إضافة CSRF token إذا كان متوفر
            var csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (csrfToken) {
                xhr.setRequestHeader('X-CSRF-TOKEN', csrfToken.getAttribute('content'));
            }
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        if (modalBody) {
                            modalBody.innerHTML = xhr.responseText;
                        }
                        console.log('AJAX request successful');
                    } else {
                        console.error('AJAX request failed:', xhr.status, xhr.statusText);
                        if (modalBody) {
                            modalBody.innerHTML = '<div class="alert alert-danger">حدث خطأ أثناء تحميل الصفحة. يرجى المحاولة مرة أخرى.</div>';
                        }
                        safeShowToastr('error', 'فشل في تحميل صفحة الدفع', 'error');
                    }
                }
            };
            
            xhr.send();
            
        } catch (error) {
            console.error('Error in AJAX popup handler:', error);
            safeShowToastr('error', 'حدث خطأ أثناء فتح نافذة الدفع', 'error');
        }
    }
    
    // تهيئة المعالجات عند تحميل الصفحة
    function initializeHandlers() {
        console.log('Initializing POS payment handlers...');
        
        // إزالة المعالجات القديمة لتجنب التكرار
        var paymentButton = document.getElementById('payment');
        if (paymentButton) {
            // إزالة المعالجات القديمة
            var newButton = paymentButton.cloneNode(true);
            paymentButton.parentNode.replaceChild(newButton, paymentButton);
            
            // إضافة المعالج الجديد
            newButton.addEventListener('click', handleAjaxPopup);
            console.log('Payment button handler attached');
        } else {
            console.warn('Payment button not found');
        }
    }
    
    // تشغيل التهيئة عند تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeHandlers);
    } else {
        initializeHandlers();
    }
    
    // تشغيل التهيئة عند تحميل jQuery أيضاً
    if (typeof $ !== 'undefined') {
        $(document).ready(initializeHandlers);
    }
    
    // إضافة دالة للتشخيص
    window.debugPaymentButton = function() {
        console.log('=== Payment Button Debug ===');
        var button = document.getElementById('payment');
        console.log('Button exists:', !!button);
        if (button) {
            console.log('Button disabled:', button.disabled);
            console.log('Button classes:', button.className);
            console.log('Button data-url:', button.getAttribute('data-url'));
        }
        
        var cartItems = document.querySelectorAll('#tbody tr:not(.no-found)');
        console.log('Cart items:', cartItems.length);
        
        var customerId = document.getElementById('vc_name_hidden');
        var warehouseId = document.getElementById('warehouse_name_hidden');
        console.log('Customer ID:', customerId ? customerId.value : 'Not found');
        console.log('Warehouse ID:', warehouseId ? warehouseId.value : 'Not found');
    };
    
    console.log('POS Payment Fix Script Ready. Use debugPaymentButton() for diagnosis.');
    
})();
